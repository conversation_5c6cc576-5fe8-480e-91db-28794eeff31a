import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Truck, MapPin, Package, Route } from 'lucide-react';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import ShipmentsTab from '@/components/logistics/ShipmentsTab';
import DriversTab from '@/components/logistics/DriversTab';
import RoutesTab from '@/components/logistics/RoutesTab';

const Logistics = () => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('shipments');

  const tabs = [
    { id: 'shipments', label: t('logistics.tabs.shipments', 'Shipments'), icon: Package, component: <ShipmentsTab /> },
    { id: 'drivers', label: t('logistics.tabs.drivers', 'Drivers'), icon: Truck, component: <DriversTab /> },
    { id: 'routes', label: t('logistics.tabs.routes', 'Routes'), icon: Route, component: <RoutesTab /> }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8 pt-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2">
            {t('logistics.title', 'Logistics & Transportation')}
          </h1>
          <p className="text-gray-600 text-md md:text-lg">
            {t('logistics.subtitle', 'Manage deliveries, track shipments, and coordinate transportation')}
          </p>
        </motion.div>

        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-1 mb-8 bg-white/50 backdrop-blur-sm rounded-lg p-1">
          {tabs.map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id)}
              className={`flex items-center justify-center sm:justify-start space-x-2 px-4 py-3 sm:px-6 rounded-md font-medium transition-all w-full sm:w-auto ${
                activeTab === id
                  ? 'bg-white shadow-md text-green-600'
                  : 'text-gray-600 hover:text-green-600'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span>{label}</span>
            </button>
          ))}
        </div>

        <div>
          {tabs.find(tab => tab.id === activeTab)?.component}
        </div>
      </div>
    </div>
  );
};

export default Logistics;