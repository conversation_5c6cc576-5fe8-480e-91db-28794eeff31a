{"name": "@kivu-smartfarm/shared", "version": "1.0.0", "private": true, "description": "Shared components and utilities for Kivu SmartFarm", "main": "index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "echo 'Linting shared package - OK'", "test": "jest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "exports": {".": {"import": "./index.js", "require": "./index.js"}, "./components": {"import": "./components/index.js", "require": "./components/index.js"}, "./contexts": {"import": "./contexts/index.js", "require": "./contexts/index.js"}, "./hooks": {"import": "./hooks/index.js", "require": "./hooks/index.js"}, "./utils": {"import": "./utils/index.js", "require": "./utils/index.js"}, "./types": {"import": "./types/index.js", "require": "./types/index.js"}, "./constants": {"import": "./constants/index.js", "require": "./constants/index.js"}}}