import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Lock, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLanguage } from '@kivu-smartfarm/shared';

const StripePayment = ({ amount, onSuccess, onError }) => {
  const { t } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [cardData, setCardData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: ''
  });

  const handleInputChange = (field, value) => {
    let formattedValue = value;

    // Format card number with spaces
    if (field === 'cardNumber') {
      formattedValue = value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim();
      if (formattedValue.length > 19) formattedValue = formattedValue.slice(0, 19);
    }

    // Format expiry date as MM/YY
    if (field === 'expiryDate') {
      formattedValue = value.replace(/\D/g, '').replace(/(\d{2})(\d)/, '$1/$2');
      if (formattedValue.length > 5) formattedValue = formattedValue.slice(0, 5);
    }

    // Limit CVV to 4 digits
    if (field === 'cvv') {
      formattedValue = value.replace(/\D/g, '').slice(0, 4);
    }

    setCardData(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  };

  const validateCard = () => {
    const { cardNumber, expiryDate, cvv, cardholderName } = cardData;
    
    if (!cardNumber || cardNumber.replace(/\s/g, '').length < 16) {
      return 'Please enter a valid card number';
    }
    
    if (!expiryDate || expiryDate.length < 5) {
      return 'Please enter a valid expiry date';
    }
    
    if (!cvv || cvv.length < 3) {
      return 'Please enter a valid CVV';
    }
    
    if (!cardholderName.trim()) {
      return 'Please enter the cardholder name';
    }
    
    return null;
  };

  const getCardType = (number) => {
    const cleanNumber = number.replace(/\s/g, '');
    
    if (cleanNumber.startsWith('4')) return 'visa';
    if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) return 'mastercard';
    if (cleanNumber.startsWith('3')) return 'amex';
    
    return 'unknown';
  };

  const handlePayment = async () => {
    const validationError = validateCard();
    if (validationError) {
      onError(validationError);
      return;
    }

    setLoading(true);

    try {
      // Simulate Stripe payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate success (95% success rate)
      if (Math.random() > 0.05) {
        onSuccess({
          transactionId: `stripe_${Date.now()}`,
          cardType: getCardType(cardData.cardNumber),
          last4: cardData.cardNumber.slice(-4),
          amount: amount
        });
      } else {
        throw new Error('Payment declined. Please check your card details and try again.');
      }
    } catch (error) {
      onError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const cardType = getCardType(cardData.cardNumber);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="w-5 h-5 mr-2 text-blue-600" />
          {t('payment.credit_card', 'Credit/Debit Card')}
        </CardTitle>
        <CardDescription>
          {t('payment.card_description', 'Pay securely with your credit or debit card')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="cardNumber">
            {t('payment.card_number', 'Card Number')}
          </Label>
          <div className="relative">
            <Input
              id="cardNumber"
              placeholder="1234 5678 9012 3456"
              value={cardData.cardNumber}
              onChange={(e) => handleInputChange('cardNumber', e.target.value)}
              className="pr-12"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {cardType === 'visa' && <span className="text-blue-600 font-bold text-sm">VISA</span>}
              {cardType === 'mastercard' && <span className="text-red-600 font-bold text-sm">MC</span>}
              {cardType === 'amex' && <span className="text-green-600 font-bold text-sm">AMEX</span>}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="expiryDate">
              {t('payment.expiry_date', 'Expiry Date')}
            </Label>
            <Input
              id="expiryDate"
              placeholder="MM/YY"
              value={cardData.expiryDate}
              onChange={(e) => handleInputChange('expiryDate', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="cvv">
              {t('payment.cvv', 'CVV')}
            </Label>
            <Input
              id="cvv"
              placeholder="123"
              value={cardData.cvv}
              onChange={(e) => handleInputChange('cvv', e.target.value)}
              type="password"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="cardholderName">
            {t('payment.cardholder_name', 'Cardholder Name')}
          </Label>
          <Input
            id="cardholderName"
            placeholder="John Doe"
            value={cardData.cardholderName}
            onChange={(e) => handleInputChange('cardholderName', e.target.value)}
          />
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-start space-x-2">
            <Lock className="w-5 h-5 text-gray-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-gray-900">
                {t('payment.secure_payment', 'Secure Payment')}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {t('payment.security_message', 'Your payment information is encrypted and secure. We never store your card details.')}
              </p>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-4">
            <span className="font-medium">{t('payment.amount_to_pay', 'Amount to Pay')}</span>
            <span className="text-xl font-bold text-green-600">
              ${amount?.toFixed(2)}
            </span>
          </div>

          <Button
            onClick={handlePayment}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700"
            size="lg"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('payment.processing', 'Processing Payment...')}
              </div>
            ) : (
              <div className="flex items-center">
                <Lock className="w-4 h-4 mr-2" />
                {t('payment.pay_securely', 'Pay Securely')}
              </div>
            )}
          </Button>
        </div>

        <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
          <span>Powered by Stripe</span>
          <span>•</span>
          <span>256-bit SSL encryption</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default StripePayment;
