{"version": 3, "sources": ["../../../../../node_modules/react-conditionally-render/dist/index.js", "../../../../../node_modules/react-chatbot-kit/build/index.js"], "sourcesContent": ["(()=>{\"use strict\";var e={d:(o,r)=>{for(var n in r)e.o(r,n)&&!e.o(o,n)&&Object.defineProperty(o,n,{enumerable:!0,get:r[n]})},o:(e,o)=>Object.prototype.hasOwnProperty.call(e,o),r:e=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})}},o={};e.r(o),e.d(o,{default:()=>r});const r=function(e){var o=e.condition,r=e.show,n=e.elseShow,t=function(e){return\"function\"==typeof e},u=function(e){return e()||(console.warn(\"Nothing was returned from your render function. Please make sure you are returning a valid React element.\"),null)};return o?t(r)?u(r):r:!o&&n?t(n)?u(n):n:null};module.exports=o})();", "(()=>{\"use strict\";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var a in r)e.o(r,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:r[a]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})}},t={};e.r(t),e.d(t,{Chatbot:()=>B,createChatBotMessage:()=>i,createClientMessage:()=>u,createCustomMessage:()=>l,default:()=>H,useChatbot:()=>T});const r=require(\"react\");var a=e.n(r);const n=require(\"react-conditionally-render\");var o=e.n(n),s=function(){return s=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},s.apply(this,arguments)},c=function(e,t){return{message:e,type:t,id:Math.round(Date.now()*Math.random())}},i=function(e,t){return s(s(s({},c(e,\"bot\")),t),{loading:!0})},l=function(e,t,r){return s(s({},c(e,t)),r)},u=function(e,t){return s(s({},c(e,\"user\")),t)},m=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(e)return e.apply(void 0,t)};function g(){return g=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},g.apply(this,arguments)}const d=({styles:e={},...t})=>a().createElement(\"svg\",g({xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 512 512\"},t),a().createElement(\"path\",{d:\"M256 288c79.5 0 144-64.5 144-144S335.5 0 256 0 112 64.5 112 144s64.5 144 144 144zm128 32h-55.1c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16H128C57.3 320 0 377.3 0 448v16c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48v-16c0-70.7-57.3-128-128-128z\"})),f=function(e){var t=e.message,r=e.customComponents;return a().createElement(\"div\",{className:\"react-chatbot-kit-user-chat-message-container\"},a().createElement(o(),{condition:!!r.userChatMessage,show:m(r.userChatMessage,{message:t}),elseShow:a().createElement(\"div\",{className:\"react-chatbot-kit-user-chat-message\"},t,a().createElement(\"div\",{className:\"react-chatbot-kit-user-chat-message-arrow\"}))}),a().createElement(o(),{condition:!!r.userAvatar,show:m(r.userAvatar),elseShow:a().createElement(\"div\",{className:\"react-chatbot-kit-user-avatar\"},a().createElement(\"div\",{className:\"react-chatbot-kit-user-avatar-container\"},a().createElement(d,{className:\"react-chatbot-kit-user-avatar-icon\"})))}))},h=function(){return a().createElement(\"div\",{className:\"react-chatbot-kit-chat-bot-avatar\"},a().createElement(\"div\",{className:\"react-chatbot-kit-chat-bot-avatar-container\"},a().createElement(\"p\",{className:\"react-chatbot-kit-chat-bot-avatar-letter\"},\"B\")))},p=function(){return a().createElement(\"div\",{className:\"chatbot-loader-container\"},a().createElement(\"svg\",{id:\"dots\",width:\"50px\",height:\"21px\",viewBox:\"0 0 132 58\",version:\"1.1\",xmlns:\"http://www.w3.org/2000/svg\"},a().createElement(\"g\",{stroke:\"none\",fill:\"none\"},a().createElement(\"g\",{id:\"chatbot-loader\",fill:\"#fff\"},a().createElement(\"circle\",{id:\"chatbot-loader-dot1\",cx:\"25\",cy:\"30\",r:\"13\"}),a().createElement(\"circle\",{id:\"chatbot-loader-dot2\",cx:\"65\",cy:\"30\",r:\"13\"}),a().createElement(\"circle\",{id:\"chatbot-loader-dot3\",cx:\"105\",cy:\"30\",r:\"13\"})))))};var v=function(){return v=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},v.apply(this,arguments)};const y=function(e){var t=e.message,n=e.withAvatar,s=void 0===n||n,c=e.loading,i=e.messages,l=e.customComponents,u=e.setState,g=e.customStyles,d=e.delay,f=e.id,y=(0,r.useState)(!1),b=y[0],w=y[1];(0,r.useEffect)((function(){var e;return function(t,r){var a=750;d&&(a+=d),e=setTimeout((function(){var e=function(e,t,r){if(r||2===arguments.length)for(var a,n=0,o=t.length;n<o;n++)!a&&n in t||(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))}([],t,!0).find((function(e){return e.id===f}));e&&(e.loading=!1,e.delay=void 0,r((function(t){var r=t.messages,a=r.findIndex((function(e){return e.id===f}));return r[a]=e,v(v({},t),{messages:r})})))}),a)}(i,u),function(){clearTimeout(e)}}),[d,f]),(0,r.useEffect)((function(){d?setTimeout((function(){return w(!0)}),d):w(!0)}),[d]);var E={backgroundColor:\"\"},P={borderRightColor:\"\"};return g&&(E.backgroundColor=g.backgroundColor,P.borderRightColor=g.backgroundColor),a().createElement(o(),{condition:b,show:a().createElement(\"div\",{className:\"react-chatbot-kit-chat-bot-message-container\"},a().createElement(o(),{condition:s,show:a().createElement(o(),{condition:!!(null==l?void 0:l.botAvatar),show:m(null==l?void 0:l.botAvatar),elseShow:a().createElement(h,null)})}),a().createElement(o(),{condition:!!(null==l?void 0:l.botChatMessage),show:m(null==l?void 0:l.botChatMessage,{message:t,loader:a().createElement(p,null)}),elseShow:a().createElement(\"div\",{className:\"react-chatbot-kit-chat-bot-message\",style:E},a().createElement(o(),{condition:c,show:a().createElement(p,null),elseShow:a().createElement(\"span\",null,t)}),a().createElement(o(),{condition:s,show:a().createElement(\"div\",{className:\"react-chatbot-kit-chat-bot-message-arrow\",style:P})}))}))})};function b(){return b=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},b.apply(this,arguments)}const w=({styles:e={},...t})=>a().createElement(\"svg\",b({xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 512 512\"},t),a().createElement(\"path\",{d:\"M476 3.2L12.5 270.6c-18.1 10.4-15.8 35.6 2.2 43.2L121 358.4l287.3-253.2c5.5-4.9 13.3 2.6 8.6 8.3L176 407v80.5c0 23.6 28.5 32.9 42.5 15.8L282 426l124.6 52.2c14.2 6 30.4-2.9 33-18.2l72-432C515 7.8 493.3-6.8 476 3.2z\"}));var E=function(){return E=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},E.apply(this,arguments)},P=function(e,t,r){if(r||2===arguments.length)for(var a,n=0,o=t.length;n<o;n++)!a&&n in t||(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))};const S=function(e){var t=e.state,n=e.setState,s=e.widgetRegistry,i=e.messageParser,l=e.parse,u=e.customComponents,m=e.actionProvider,g=e.botName,d=e.customStyles,h=e.headerText,p=e.customMessages,v=e.placeholderText,b=e.validator,S=e.disableScrollToBottom,O=e.messageHistory,k=e.actions,M=e.messageContainerRef,C=t.messages,N=(0,r.useState)(\"\"),x=N[0],j=N[1],T=function(){setTimeout((function(){var e;M.current&&(M.current.scrollTop=null===(e=null==M?void 0:M.current)||void 0===e?void 0:e.scrollHeight)}),50)};(0,r.useEffect)((function(){S||T()}));var A=function(){n((function(e){return E(E({},e),{messages:P(P([],e.messages,!0),[c(x,\"user\")],!1)})})),T(),j(\"\")},B={backgroundColor:\"\"};d&&d.chatButton&&(B.backgroundColor=d.chatButton.backgroundColor);var H=\"Conversation with \"+g;h&&(H=h);var I=\"Write your message here\";return v&&(I=v),a().createElement(\"div\",{className:\"react-chatbot-kit-chat-container\"},a().createElement(\"div\",{className:\"react-chatbot-kit-chat-inner-container\"},a().createElement(o(),{condition:!!u.header,show:u.header&&u.header(m),elseShow:a().createElement(\"div\",{className:\"react-chatbot-kit-chat-header\"},H)}),a().createElement(\"div\",{className:\"react-chatbot-kit-chat-message-container\",ref:M},a().createElement(o(),{condition:\"string\"==typeof O&&Boolean(O),show:a().createElement(\"div\",{dangerouslySetInnerHTML:{__html:O}})}),C.map((function(e,r){return\"bot\"===e.type?a().createElement(a().Fragment,{key:e.id},function(e,r){var c;c=e.withAvatar?e.withAvatar:function(e,t){if(0===t)return!0;var r=e[t-1];return!(\"bot\"===r.type&&!r.widget)}(C,r);var i=E(E({},e),{setState:n,state:t,customComponents:u,widgetRegistry:s,messages:C,actions:k});if(e.widget){var l=s.getWidget(i.widget,E(E({},t),{scrollIntoView:T,payload:e.payload,actions:k}));return a().createElement(a().Fragment,null,a().createElement(y,E({customStyles:d.botMessageBox,withAvatar:c},i,{key:e.id})),a().createElement(o(),{condition:!i.loading,show:l||null}))}return a().createElement(y,E({customStyles:d.botMessageBox,key:e.id,withAvatar:c},i,{customComponents:u,messages:C,setState:n}))}(e,r)):\"user\"===e.type?a().createElement(a().Fragment,{key:e.id},function(e){var r=s.getWidget(e.widget,E(E({},t),{scrollIntoView:T,payload:e.payload,actions:k}));return a().createElement(a().Fragment,null,a().createElement(f,{message:e.message,key:e.id,customComponents:u}),r||null)}(e)):function(e,t){return!!t[e.type]}(e,p)?a().createElement(a().Fragment,{key:e.id},function(e){var r=p[e.type],o={setState:n,state:t,scrollIntoView:T,actionProvider:m,payload:e.payload,actions:k};if(e.widget){var c=s.getWidget(e.widget,E(E({},t),{scrollIntoView:T,payload:e.payload,actions:k}));return a().createElement(a().Fragment,null,r(o),c||null)}return r(o)}(e)):void 0})),a().createElement(\"div\",{style:{paddingBottom:\"15px\"}})),a().createElement(\"div\",{className:\"react-chatbot-kit-chat-input-container\"},a().createElement(\"form\",{className:\"react-chatbot-kit-chat-input-form\",onSubmit:function(e){if(e.preventDefault(),b&&\"function\"==typeof b){if(b(x)){if(A(),l)return l(x);i.parse(x)}}else{if(A(),l)return l(x);i.parse(x)}}},a().createElement(\"input\",{className:\"react-chatbot-kit-chat-input\",placeholder:I,value:x,onChange:function(e){return j(e.target.value)}}),a().createElement(\"button\",{className:\"react-chatbot-kit-chat-btn-send\",style:B},a().createElement(w,{className:\"react-chatbot-kit-chat-btn-send-icon\"}))))))},O=function(e){var t=e.message;return a().createElement(\"div\",{className:\"react-chatbot-kit-error\"},a().createElement(\"h1\",{className:\"react-chatbot-kit-error-header\"},\"Ooops. Something is missing.\"),a().createElement(\"div\",{className:\"react-chatbot-kit-error-container\"},a().createElement(y,{message:t,withAvatar:!0,loading:!1,id:1,customStyles:{backgroundColor:\"\"},messages:[]})),a().createElement(\"a\",{href:\"https://fredrikoseberg.github.io/react-chatbot-kit-docs/\",rel:\"noopener norefferer\",target:\"_blank\",className:\"react-chatbot-kit-error-docs\"},\"View the docs\"))};var k=function(e){return e.widgets?e.widgets:[]},M=function(e){try{new e}catch(e){return!1}return!0},C=function(){return C=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},C.apply(this,arguments)};const N=function(e,t){var r=this;this.addWidget=function(e,t){var a=e.widgetName,n=e.widgetFunc,o=e.mapStateToProps,s=e.props;r[a]={widget:n,props:s,mapStateToProps:o,parentProps:C({},t)}},this.getWidget=function(e,t){var a=r[e];if(a){var n,o=C(C(C(C({scrollIntoView:t.scrollIntoView},a.parentProps),\"object\"==typeof(n=a.props)?n:{}),r.mapStateToProps(a.mapStateToProps,t)),{setState:r.setState,actionProvider:r.actionProvider||t.actions,actions:t.actions,state:t,payload:t.payload});return a.widget(o)||null}},this.mapStateToProps=function(e,t){if(e)return e.reduce((function(e,r){return e[r]=t[r],e}),{})},this.setState=e,this.actionProvider=t};var x=function(){return x=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},x.apply(this,arguments)},j=function(e,t,r){if(r||2===arguments.length)for(var a,n=0,o=t.length;n<o;n++)!a&&n in t||(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))};const T=function(e){var t=e.config,n=e.actionProvider,o=e.messageParser,s=e.messageHistory,c=e.runInitialMessagesWithHistory,m=e.saveMessages,g=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var n=0;for(a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]])}return r}(e,[\"config\",\"actionProvider\",\"messageParser\",\"messageHistory\",\"runInitialMessagesWithHistory\",\"saveMessages\"]),d=\"\",f=\"\";if(!t||!n||!o)return{configurationError:d=\"I think you forgot to feed me some props. Did you remember to pass a config, a messageparser and an actionprovider?\"};var h=function(e,t){var r=[];return e.initialMessages||r.push(\"Config must contain property 'initialMessages', and it expects it to be an array of chatbotmessages.\"),r}(t);if(h.length)return{invalidPropsError:f=h.reduce((function(e,t){return e+t}),\"\")};var p=function(e){return e.state?e.state:{}}(t);s&&Array.isArray(s)?t.initialMessages=j([],s,!0):\"string\"==typeof s&&Boolean(s)&&(c||(t.initialMessages=[]));var v,y,b,w=a().useState(x({messages:j([],t.initialMessages,!0)},p)),E=w[0],P=w[1],S=a().useRef(E.messages),O=a().useRef(),C=a().useRef();(0,r.useEffect)((function(){S.current=E.messages})),(0,r.useEffect)((function(){s&&Array.isArray(s)&&P((function(e){return x(x({},e),{messages:s})}))}),[]),(0,r.useEffect)((function(){var e=C.current;return function(){if(m&&\"function\"==typeof m){var t=e.innerHTML.toString();m(S.current,t)}}}),[]),(0,r.useEffect)((function(){O.current=E}),[E]);var T=n,A=o;return M(T)&&M(A)?(v=new n(i,P,u,O.current,l,g),y=new N(P,v),b=new o(v,O.current),k(t).forEach((function(e){return null==y?void 0:y.addWidget(e,g)}))):(v=n,b=o,y=new N(P,null),k(t).forEach((function(e){return null==y?void 0:y.addWidget(e,g)}))),{widgetRegistry:y,actionProv:v,messagePars:b,configurationError:d,invalidPropsError:f,state:E,setState:P,messageContainerRef:C,ActionProvider:T,MessageParser:A}};var A=function(){return A=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},A.apply(this,arguments)};const B=function(e){var t=e.actionProvider,r=e.messageParser,n=e.config,o=e.headerText,s=e.placeholderText,c=e.saveMessages,l=e.messageHistory,u=e.runInitialMessagesWithHistory,m=e.disableScrollToBottom,g=e.validator,d=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var n=0;for(a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]])}return r}(e,[\"actionProvider\",\"messageParser\",\"config\",\"headerText\",\"placeholderText\",\"saveMessages\",\"messageHistory\",\"runInitialMessagesWithHistory\",\"disableScrollToBottom\",\"validator\"]),f=T(A({config:n,actionProvider:t,messageParser:r,messageHistory:l,saveMessages:c,runInitialMessagesWithHistory:u},d)),h=f.configurationError,p=f.invalidPropsError,v=f.ActionProvider,y=f.MessageParser,b=f.widgetRegistry,w=f.messageContainerRef,E=f.actionProv,P=f.messagePars,k=f.state,C=f.setState;if(h)return a().createElement(O,{message:h});if(p.length)return a().createElement(O,{message:p});var N=function(e){return e.customStyles?e.customStyles:{}}(n),x=function(e){return e.customComponents?e.customComponents:{}}(n),j=function(e){return e.botName?e.botName:\"Bot\"}(n),B=function(e){return e.customMessages?e.customMessages:{}}(n);return M(v)&&M(y)?a().createElement(S,{state:k,setState:C,widgetRegistry:b,actionProvider:E,messageParser:P,customMessages:B,customComponents:A({},x),botName:j,customStyles:A({},N),headerText:o,placeholderText:s,validator:g,messageHistory:l,disableScrollToBottom:m,messageContainerRef:w}):a().createElement(v,{state:k,setState:C,createChatBotMessage:i},a().createElement(y,null,a().createElement(S,{state:k,setState:C,widgetRegistry:b,actionProvider:v,messageParser:y,customMessages:B,customComponents:A({},x),botName:j,customStyles:A({},N),headerText:o,placeholderText:s,validator:g,messageHistory:l,disableScrollToBottom:m,messageContainerRef:w})))},H=B;module.exports=t})();"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,MAAI;AAAC;AAAa,UAAI,IAAE,EAAC,GAAE,CAACA,IAAEC,OAAI;AAAC,iBAAQ,KAAKA;AAAE,YAAE,EAAEA,IAAE,CAAC,KAAG,CAAC,EAAE,EAAED,IAAE,CAAC,KAAG,OAAO,eAAeA,IAAE,GAAE,EAAC,YAAW,MAAG,KAAIC,GAAE,CAAC,EAAC,CAAC;AAAA,MAAC,GAAE,GAAE,CAACC,IAAEF,OAAI,OAAO,UAAU,eAAe,KAAKE,IAAEF,EAAC,GAAE,GAAE,CAAAE,OAAG;AAAC,uBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,MAAC,EAAC,GAAE,IAAE,CAAC;AAAE,QAAE,EAAE,CAAC,GAAE,EAAE,EAAE,GAAE,EAAC,SAAQ,MAAI,EAAC,CAAC;AAAE,YAAM,IAAE,SAASA,IAAE;AAAC,YAAIF,KAAEE,GAAE,WAAUD,KAAEC,GAAE,MAAK,IAAEA,GAAE,UAAS,IAAE,SAASA,IAAE;AAAC,iBAAM,cAAY,OAAOA;AAAA,QAAC,GAAE,IAAE,SAASA,IAAE;AAAC,iBAAOA,GAAE,MAAI,QAAQ,KAAK,2GAA2G,GAAE;AAAA,QAAK;AAAE,eAAOF,KAAE,EAAEC,EAAC,IAAE,EAAEA,EAAC,IAAEA,KAAE,CAACD,MAAG,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,IAAE;AAAA,MAAI;AAAE,aAAO,UAAQ;AAAA,IAAC,GAAG;AAAA;AAAA;;;ACA3rB;AAAA;AAAA,KAAC,MAAI;AAAC;AAAa,UAAI,IAAE,EAAC,GAAE,CAAAG,OAAG;AAAC,YAAIC,KAAED,MAAGA,GAAE,aAAW,MAAIA,GAAE,UAAQ,MAAIA;AAAE,eAAO,EAAE,EAAEC,IAAE,EAAC,GAAEA,GAAC,CAAC,GAAEA;AAAA,MAAC,GAAE,GAAE,CAACD,IAAEC,OAAI;AAAC,iBAAQC,MAAKD;AAAE,YAAE,EAAEA,IAAEC,EAAC,KAAG,CAAC,EAAE,EAAEF,IAAEE,EAAC,KAAG,OAAO,eAAeF,IAAEE,IAAE,EAAC,YAAW,MAAG,KAAID,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,GAAE,CAACC,IAAEH,OAAI,OAAO,UAAU,eAAe,KAAKG,IAAEH,EAAC,GAAE,GAAE,CAAAG,OAAG;AAAC,uBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,MAAC,EAAC,GAAE,IAAE,CAAC;AAAE,QAAE,EAAE,CAAC,GAAE,EAAE,EAAE,GAAE,EAAC,SAAQ,MAAI,GAAE,sBAAqB,MAAI,GAAE,qBAAoB,MAAI,GAAE,qBAAoB,MAAI,GAAE,SAAQ,MAAI,GAAE,YAAW,MAAI,EAAC,CAAC;AAAE,YAAM,IAAE;AAAiB,UAAI,IAAE,EAAE,EAAE,CAAC;AAAE,YAAM,IAAE;AAAsC,UAAI,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,WAAU;AAAC,eAAO,IAAE,OAAO,UAAQ,SAASA,IAAE;AAAC,mBAAQH,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED;AAAI,qBAAQG,MAAKJ,KAAE,UAAUC,EAAC;AAAE,qBAAO,UAAU,eAAe,KAAKD,IAAEI,EAAC,MAAID,GAAEC,EAAC,IAAEJ,GAAEI,EAAC;AAAG,iBAAOD;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC,GAAE,IAAE,SAASA,IAAEH,IAAE;AAAC,eAAM,EAAC,SAAQG,IAAE,MAAKH,IAAE,IAAG,KAAK,MAAM,KAAK,IAAI,IAAE,KAAK,OAAO,CAAC,EAAC;AAAA,MAAC,GAAE,IAAE,SAASG,IAAEH,IAAE;AAAC,eAAO,EAAE,EAAE,EAAE,CAAC,GAAE,EAAEG,IAAE,KAAK,CAAC,GAAEH,EAAC,GAAE,EAAC,SAAQ,KAAE,CAAC;AAAA,MAAC,GAAE,IAAE,SAASG,IAAEH,IAAEC,IAAE;AAAC,eAAO,EAAE,EAAE,CAAC,GAAE,EAAEE,IAAEH,EAAC,CAAC,GAAEC,EAAC;AAAA,MAAC,GAAE,IAAE,SAASE,IAAEH,IAAE;AAAC,eAAO,EAAE,EAAE,CAAC,GAAE,EAAEG,IAAE,MAAM,CAAC,GAAEH,EAAC;AAAA,MAAC,GAAE,IAAE,SAASG,IAAE;AAAC,iBAAQH,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAE,UAAU,QAAOA;AAAI,UAAAD,GAAEC,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,YAAGE;AAAE,iBAAOA,GAAE,MAAM,QAAOH,EAAC;AAAA,MAAC;AAAE,eAAS,IAAG;AAAC,eAAO,IAAE,OAAO,UAAQ,SAASG,IAAE;AAAC,mBAAQH,KAAE,GAAEA,KAAE,UAAU,QAAOA,MAAI;AAAC,gBAAIC,KAAE,UAAUD,EAAC;AAAE,qBAAQE,MAAKD;AAAE,qBAAO,UAAU,eAAe,KAAKA,IAAEC,EAAC,MAAIC,GAAED,EAAC,IAAED,GAAEC,EAAC;AAAA,UAAE;AAAC,iBAAOC;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC;AAAC,YAAM,IAAE,CAAC,EAAC,QAAOA,KAAE,CAAC,GAAE,GAAGH,GAAC,MAAI,EAAE,EAAE,cAAc,OAAM,EAAE,EAAC,OAAM,8BAA6B,SAAQ,cAAa,GAAEA,EAAC,GAAE,EAAE,EAAE,cAAc,QAAO,EAAC,GAAE,uPAAsP,CAAC,CAAC,GAAE,IAAE,SAASG,IAAE;AAAC,YAAIH,KAAEG,GAAE,SAAQF,KAAEE,GAAE;AAAiB,eAAO,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,gDAA+C,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAU,CAAC,CAACF,GAAE,iBAAgB,MAAK,EAAEA,GAAE,iBAAgB,EAAC,SAAQD,GAAC,CAAC,GAAE,UAAS,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,sCAAqC,GAAEA,IAAE,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,4CAA2C,CAAC,CAAC,EAAC,CAAC,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAU,CAAC,CAACC,GAAE,YAAW,MAAK,EAAEA,GAAE,UAAU,GAAE,UAAS,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,gCAA+B,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,0CAAyC,GAAE,EAAE,EAAE,cAAc,GAAE,EAAC,WAAU,qCAAoC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AAAA,MAAC,GAAE,IAAE,WAAU;AAAC,eAAO,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,oCAAmC,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,8CAA6C,GAAE,EAAE,EAAE,cAAc,KAAI,EAAC,WAAU,2CAA0C,GAAE,GAAG,CAAC,CAAC;AAAA,MAAC,GAAE,IAAE,WAAU;AAAC,eAAO,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,2BAA0B,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,IAAG,QAAO,OAAM,QAAO,QAAO,QAAO,SAAQ,cAAa,SAAQ,OAAM,OAAM,6BAA4B,GAAE,EAAE,EAAE,cAAc,KAAI,EAAC,QAAO,QAAO,MAAK,OAAM,GAAE,EAAE,EAAE,cAAc,KAAI,EAAC,IAAG,kBAAiB,MAAK,OAAM,GAAE,EAAE,EAAE,cAAc,UAAS,EAAC,IAAG,uBAAsB,IAAG,MAAK,IAAG,MAAK,GAAE,KAAI,CAAC,GAAE,EAAE,EAAE,cAAc,UAAS,EAAC,IAAG,uBAAsB,IAAG,MAAK,IAAG,MAAK,GAAE,KAAI,CAAC,GAAE,EAAE,EAAE,cAAc,UAAS,EAAC,IAAG,uBAAsB,IAAG,OAAM,IAAG,MAAK,GAAE,KAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MAAC;AAAE,UAAI,IAAE,WAAU;AAAC,eAAO,IAAE,OAAO,UAAQ,SAASE,IAAE;AAAC,mBAAQH,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED;AAAI,qBAAQG,MAAKJ,KAAE,UAAUC,EAAC;AAAE,qBAAO,UAAU,eAAe,KAAKD,IAAEI,EAAC,MAAID,GAAEC,EAAC,IAAEJ,GAAEI,EAAC;AAAG,iBAAOD;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC;AAAE,YAAM,IAAE,SAASA,IAAE;AAAC,YAAIH,KAAEG,GAAE,SAAQC,KAAED,GAAE,YAAWE,KAAE,WAASD,MAAGA,IAAEE,KAAEH,GAAE,SAAQI,KAAEJ,GAAE,UAASK,KAAEL,GAAE,kBAAiBM,KAAEN,GAAE,UAASO,KAAEP,GAAE,cAAaQ,KAAER,GAAE,OAAMS,KAAET,GAAE,IAAGU,MAAG,GAAE,EAAE,UAAU,KAAE,GAAEC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,SAAC,GAAE,EAAE,WAAY,WAAU;AAAC,cAAIV;AAAE,iBAAO,SAASH,IAAEC,IAAE;AAAC,gBAAIC,KAAE;AAAI,YAAAS,OAAIT,MAAGS,KAAGR,KAAE,WAAY,WAAU;AAAC,kBAAIA,KAAE,SAASA,IAAEH,IAAEC,IAAE;AAAC,oBAAGA,MAAG,MAAI,UAAU;AAAO,2BAAQC,IAAEE,KAAE,GAAEY,KAAEhB,GAAE,QAAOI,KAAEY,IAAEZ;AAAI,qBAACF,MAAGE,MAAKJ,OAAIE,OAAIA,KAAE,MAAM,UAAU,MAAM,KAAKF,IAAE,GAAEI,EAAC,IAAGF,GAAEE,EAAC,IAAEJ,GAAEI,EAAC;AAAG,uBAAOD,GAAE,OAAOD,MAAG,MAAM,UAAU,MAAM,KAAKF,EAAC,CAAC;AAAA,cAAC,EAAE,CAAC,GAAEA,IAAE,IAAE,EAAE,KAAM,SAASG,IAAE;AAAC,uBAAOA,GAAE,OAAKS;AAAA,cAAC,CAAE;AAAE,cAAAT,OAAIA,GAAE,UAAQ,OAAGA,GAAE,QAAM,QAAOF,GAAG,SAASD,IAAE;AAAC,oBAAIC,KAAED,GAAE,UAASE,KAAED,GAAE,UAAW,SAASE,IAAE;AAAC,yBAAOA,GAAE,OAAKS;AAAA,gBAAC,CAAE;AAAE,uBAAOX,GAAEC,EAAC,IAAEC,IAAE,EAAE,EAAE,CAAC,GAAEH,EAAC,GAAE,EAAC,UAASC,GAAC,CAAC;AAAA,cAAC,CAAE;AAAA,YAAE,GAAGC,EAAC;AAAA,UAAC,EAAEK,IAAEE,EAAC,GAAE,WAAU;AAAC,yBAAaN,EAAC;AAAA,UAAC;AAAA,QAAC,GAAG,CAACQ,IAAEC,EAAC,CAAC,IAAG,GAAE,EAAE,WAAY,WAAU;AAAC,UAAAD,KAAE,WAAY,WAAU;AAAC,mBAAOI,GAAE,IAAE;AAAA,UAAC,GAAGJ,EAAC,IAAEI,GAAE,IAAE;AAAA,QAAC,GAAG,CAACJ,EAAC,CAAC;AAAE,YAAIM,KAAE,EAAC,iBAAgB,GAAE,GAAEC,KAAE,EAAC,kBAAiB,GAAE;AAAE,eAAOR,OAAIO,GAAE,kBAAgBP,GAAE,iBAAgBQ,GAAE,mBAAiBR,GAAE,kBAAiB,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAUI,IAAE,MAAK,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,+CAA8C,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAUT,IAAE,MAAK,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAU,CAAC,EAAE,QAAMG,KAAE,SAAOA,GAAE,YAAW,MAAK,EAAE,QAAMA,KAAE,SAAOA,GAAE,SAAS,GAAE,UAAS,EAAE,EAAE,cAAc,GAAE,IAAI,EAAC,CAAC,EAAC,CAAC,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAU,CAAC,EAAE,QAAMA,KAAE,SAAOA,GAAE,iBAAgB,MAAK,EAAE,QAAMA,KAAE,SAAOA,GAAE,gBAAe,EAAC,SAAQR,IAAE,QAAO,EAAE,EAAE,cAAc,GAAE,IAAI,EAAC,CAAC,GAAE,UAAS,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,sCAAqC,OAAMiB,GAAC,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAUX,IAAE,MAAK,EAAE,EAAE,cAAc,GAAE,IAAI,GAAE,UAAS,EAAE,EAAE,cAAc,QAAO,MAAKN,EAAC,EAAC,CAAC,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAUK,IAAE,MAAK,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,4CAA2C,OAAMa,GAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC;AAAA,MAAC;AAAE,eAAS,IAAG;AAAC,eAAO,IAAE,OAAO,UAAQ,SAASf,IAAE;AAAC,mBAAQH,KAAE,GAAEA,KAAE,UAAU,QAAOA,MAAI;AAAC,gBAAIC,KAAE,UAAUD,EAAC;AAAE,qBAAQE,MAAKD;AAAE,qBAAO,UAAU,eAAe,KAAKA,IAAEC,EAAC,MAAIC,GAAED,EAAC,IAAED,GAAEC,EAAC;AAAA,UAAE;AAAC,iBAAOC;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC;AAAC,YAAM,IAAE,CAAC,EAAC,QAAOA,KAAE,CAAC,GAAE,GAAGH,GAAC,MAAI,EAAE,EAAE,cAAc,OAAM,EAAE,EAAC,OAAM,8BAA6B,SAAQ,cAAa,GAAEA,EAAC,GAAE,EAAE,EAAE,cAAc,QAAO,EAAC,GAAE,wNAAuN,CAAC,CAAC;AAAE,UAAI,IAAE,WAAU;AAAC,eAAO,IAAE,OAAO,UAAQ,SAASG,IAAE;AAAC,mBAAQH,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED;AAAI,qBAAQG,MAAKJ,KAAE,UAAUC,EAAC;AAAE,qBAAO,UAAU,eAAe,KAAKD,IAAEI,EAAC,MAAID,GAAEC,EAAC,IAAEJ,GAAEI,EAAC;AAAG,iBAAOD;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC,GAAE,IAAE,SAASA,IAAEH,IAAEC,IAAE;AAAC,YAAGA,MAAG,MAAI,UAAU;AAAO,mBAAQC,IAAEE,KAAE,GAAEY,KAAEhB,GAAE,QAAOI,KAAEY,IAAEZ;AAAI,aAACF,MAAGE,MAAKJ,OAAIE,OAAIA,KAAE,MAAM,UAAU,MAAM,KAAKF,IAAE,GAAEI,EAAC,IAAGF,GAAEE,EAAC,IAAEJ,GAAEI,EAAC;AAAG,eAAOD,GAAE,OAAOD,MAAG,MAAM,UAAU,MAAM,KAAKF,EAAC,CAAC;AAAA,MAAC;AAAE,YAAM,IAAE,SAASG,IAAE;AAAC,YAAIH,KAAEG,GAAE,OAAMC,KAAED,GAAE,UAASE,KAAEF,GAAE,gBAAeI,KAAEJ,GAAE,eAAcK,KAAEL,GAAE,OAAMM,KAAEN,GAAE,kBAAiBgB,KAAEhB,GAAE,gBAAeO,KAAEP,GAAE,SAAQQ,KAAER,GAAE,cAAaiB,KAAEjB,GAAE,YAAWkB,KAAElB,GAAE,gBAAemB,KAAEnB,GAAE,iBAAgBW,KAAEX,GAAE,WAAUoB,KAAEpB,GAAE,uBAAsBqB,KAAErB,GAAE,gBAAesB,KAAEtB,GAAE,SAAQuB,KAAEvB,GAAE,qBAAoBwB,KAAE3B,GAAE,UAAS4B,MAAG,GAAE,EAAE,UAAU,EAAE,GAAEC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,GAAEG,KAAE,WAAU;AAAC,qBAAY,WAAU;AAAC,gBAAI5B;AAAE,YAAAuB,GAAE,YAAUA,GAAE,QAAQ,YAAU,UAAQvB,KAAE,QAAMuB,KAAE,SAAOA,GAAE,YAAU,WAASvB,KAAE,SAAOA,GAAE;AAAA,UAAa,GAAG,EAAE;AAAA,QAAC;AAAE,SAAC,GAAE,EAAE,WAAY,WAAU;AAAC,UAAAoB,MAAGQ,GAAE;AAAA,QAAC,CAAE;AAAE,YAAIC,KAAE,WAAU;AAAC,UAAA5B,GAAG,SAASD,IAAE;AAAC,mBAAO,EAAE,EAAE,CAAC,GAAEA,EAAC,GAAE,EAAC,UAAS,EAAE,EAAE,CAAC,GAAEA,GAAE,UAAS,IAAE,GAAE,CAAC,EAAE0B,IAAE,MAAM,CAAC,GAAE,KAAE,EAAC,CAAC;AAAA,UAAC,CAAE,GAAEE,GAAE,GAAED,GAAE,EAAE;AAAA,QAAC,GAAEG,KAAE,EAAC,iBAAgB,GAAE;AAAE,QAAAtB,MAAGA,GAAE,eAAasB,GAAE,kBAAgBtB,GAAE,WAAW;AAAiB,YAAIuB,KAAE,uBAAqBxB;AAAE,QAAAU,OAAIc,KAAEd;AAAG,YAAI,IAAE;AAA0B,eAAOE,OAAI,IAAEA,KAAG,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,mCAAkC,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,yCAAwC,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAU,CAAC,CAACb,GAAE,QAAO,MAAKA,GAAE,UAAQA,GAAE,OAAOU,EAAC,GAAE,UAAS,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,gCAA+B,GAAEe,EAAC,EAAC,CAAC,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,4CAA2C,KAAIR,GAAC,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAU,YAAU,OAAOF,MAAG,QAAQA,EAAC,GAAE,MAAK,EAAE,EAAE,cAAc,OAAM,EAAC,yBAAwB,EAAC,QAAOA,GAAC,EAAC,CAAC,EAAC,CAAC,GAAEG,GAAE,IAAK,SAASxB,IAAEF,IAAE;AAAC,iBAAM,UAAQE,GAAE,OAAK,EAAE,EAAE,cAAc,EAAE,EAAE,UAAS,EAAC,KAAIA,GAAE,GAAE,GAAE,SAASA,IAAEF,IAAE;AAAC,gBAAIK;AAAE,YAAAA,KAAEH,GAAE,aAAWA,GAAE,aAAW,SAASA,IAAEH,IAAE;AAAC,kBAAG,MAAIA;AAAE,uBAAM;AAAG,kBAAIC,KAAEE,GAAEH,KAAE,CAAC;AAAE,qBAAM,EAAE,UAAQC,GAAE,QAAM,CAACA,GAAE;AAAA,YAAO,EAAE0B,IAAE1B,EAAC;AAAE,gBAAIM,KAAE,EAAE,EAAE,CAAC,GAAEJ,EAAC,GAAE,EAAC,UAASC,IAAE,OAAMJ,IAAE,kBAAiBS,IAAE,gBAAeJ,IAAE,UAASsB,IAAE,SAAQF,GAAC,CAAC;AAAE,gBAAGtB,GAAE,QAAO;AAAC,kBAAIK,KAAEH,GAAE,UAAUE,GAAE,QAAO,EAAE,EAAE,CAAC,GAAEP,EAAC,GAAE,EAAC,gBAAe+B,IAAE,SAAQ5B,GAAE,SAAQ,SAAQsB,GAAC,CAAC,CAAC;AAAE,qBAAO,EAAE,EAAE,cAAc,EAAE,EAAE,UAAS,MAAK,EAAE,EAAE,cAAc,GAAE,EAAE,EAAC,cAAad,GAAE,eAAc,YAAWL,GAAC,GAAEC,IAAE,EAAC,KAAIJ,GAAE,GAAE,CAAC,CAAC,GAAE,EAAE,EAAE,cAAc,EAAE,GAAE,EAAC,WAAU,CAACI,GAAE,SAAQ,MAAKC,MAAG,KAAI,CAAC,CAAC;AAAA,YAAC;AAAC,mBAAO,EAAE,EAAE,cAAc,GAAE,EAAE,EAAC,cAAaG,GAAE,eAAc,KAAIR,GAAE,IAAG,YAAWG,GAAC,GAAEC,IAAE,EAAC,kBAAiBE,IAAE,UAASkB,IAAE,UAASvB,GAAC,CAAC,CAAC;AAAA,UAAC,EAAED,IAAEF,EAAC,CAAC,IAAE,WAASE,GAAE,OAAK,EAAE,EAAE,cAAc,EAAE,EAAE,UAAS,EAAC,KAAIA,GAAE,GAAE,GAAE,SAASA,IAAE;AAAC,gBAAIF,KAAEI,GAAE,UAAUF,GAAE,QAAO,EAAE,EAAE,CAAC,GAAEH,EAAC,GAAE,EAAC,gBAAe+B,IAAE,SAAQ5B,GAAE,SAAQ,SAAQsB,GAAC,CAAC,CAAC;AAAE,mBAAO,EAAE,EAAE,cAAc,EAAE,EAAE,UAAS,MAAK,EAAE,EAAE,cAAc,GAAE,EAAC,SAAQtB,GAAE,SAAQ,KAAIA,GAAE,IAAG,kBAAiBM,GAAC,CAAC,GAAER,MAAG,IAAI;AAAA,UAAC,EAAEE,EAAC,CAAC,IAAE,SAASA,IAAEH,IAAE;AAAC,mBAAM,CAAC,CAACA,GAAEG,GAAE,IAAI;AAAA,UAAC,EAAEA,IAAEkB,EAAC,IAAE,EAAE,EAAE,cAAc,EAAE,EAAE,UAAS,EAAC,KAAIlB,GAAE,GAAE,GAAE,SAASA,IAAE;AAAC,gBAAIF,KAAEoB,GAAElB,GAAE,IAAI,GAAEa,KAAE,EAAC,UAASZ,IAAE,OAAMJ,IAAE,gBAAe+B,IAAE,gBAAeZ,IAAE,SAAQhB,GAAE,SAAQ,SAAQsB,GAAC;AAAE,gBAAGtB,GAAE,QAAO;AAAC,kBAAIG,KAAED,GAAE,UAAUF,GAAE,QAAO,EAAE,EAAE,CAAC,GAAEH,EAAC,GAAE,EAAC,gBAAe+B,IAAE,SAAQ5B,GAAE,SAAQ,SAAQsB,GAAC,CAAC,CAAC;AAAE,qBAAO,EAAE,EAAE,cAAc,EAAE,EAAE,UAAS,MAAKxB,GAAEe,EAAC,GAAEV,MAAG,IAAI;AAAA,YAAC;AAAC,mBAAOL,GAAEe,EAAC;AAAA,UAAC,EAAEb,EAAC,CAAC,IAAE;AAAA,QAAM,CAAE,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,OAAM,EAAC,eAAc,OAAM,EAAC,CAAC,CAAC,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,yCAAwC,GAAE,EAAE,EAAE,cAAc,QAAO,EAAC,WAAU,qCAAoC,UAAS,SAASA,IAAE;AAAC,cAAGA,GAAE,eAAe,GAAEW,MAAG,cAAY,OAAOA,IAAE;AAAC,gBAAGA,GAAEe,EAAC,GAAE;AAAC,kBAAGG,GAAE,GAAExB;AAAE,uBAAOA,GAAEqB,EAAC;AAAE,cAAAtB,GAAE,MAAMsB,EAAC;AAAA,YAAC;AAAA,UAAC,OAAK;AAAC,gBAAGG,GAAE,GAAExB;AAAE,qBAAOA,GAAEqB,EAAC;AAAE,YAAAtB,GAAE,MAAMsB,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAE,EAAE,cAAc,SAAQ,EAAC,WAAU,gCAA+B,aAAY,GAAE,OAAMA,IAAE,UAAS,SAAS1B,IAAE;AAAC,iBAAO2B,GAAE3B,GAAE,OAAO,KAAK;AAAA,QAAC,EAAC,CAAC,GAAE,EAAE,EAAE,cAAc,UAAS,EAAC,WAAU,mCAAkC,OAAM8B,GAAC,GAAE,EAAE,EAAE,cAAc,GAAE,EAAC,WAAU,uCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MAAC,GAAE,IAAE,SAAS9B,IAAE;AAAC,YAAIH,KAAEG,GAAE;AAAQ,eAAO,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,0BAAyB,GAAE,EAAE,EAAE,cAAc,MAAK,EAAC,WAAU,iCAAgC,GAAE,8BAA8B,GAAE,EAAE,EAAE,cAAc,OAAM,EAAC,WAAU,oCAAmC,GAAE,EAAE,EAAE,cAAc,GAAE,EAAC,SAAQH,IAAE,YAAW,MAAG,SAAQ,OAAG,IAAG,GAAE,cAAa,EAAC,iBAAgB,GAAE,GAAE,UAAS,CAAC,EAAC,CAAC,CAAC,GAAE,EAAE,EAAE,cAAc,KAAI,EAAC,MAAK,4DAA2D,KAAI,uBAAsB,QAAO,UAAS,WAAU,+BAA8B,GAAE,eAAe,CAAC;AAAA,MAAC;AAAE,UAAI,IAAE,SAASG,IAAE;AAAC,eAAOA,GAAE,UAAQA,GAAE,UAAQ,CAAC;AAAA,MAAC,GAAE,IAAE,SAASA,IAAE;AAAC,YAAG;AAAC,cAAIA;AAAA,QAAC,SAAOA,IAAE;AAAC,iBAAM;AAAA,QAAE;AAAC,eAAM;AAAA,MAAE,GAAE,IAAE,WAAU;AAAC,eAAO,IAAE,OAAO,UAAQ,SAASA,IAAE;AAAC,mBAAQH,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED;AAAI,qBAAQG,MAAKJ,KAAE,UAAUC,EAAC;AAAE,qBAAO,UAAU,eAAe,KAAKD,IAAEI,EAAC,MAAID,GAAEC,EAAC,IAAEJ,GAAEI,EAAC;AAAG,iBAAOD;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC;AAAE,YAAM,IAAE,SAASA,IAAEH,IAAE;AAAC,YAAIC,KAAE;AAAK,aAAK,YAAU,SAASE,IAAEH,IAAE;AAAC,cAAIE,KAAEC,GAAE,YAAWC,KAAED,GAAE,YAAWa,KAAEb,GAAE,iBAAgBE,KAAEF,GAAE;AAAM,UAAAF,GAAEC,EAAC,IAAE,EAAC,QAAOE,IAAE,OAAMC,IAAE,iBAAgBW,IAAE,aAAY,EAAE,CAAC,GAAEhB,EAAC,EAAC;AAAA,QAAC,GAAE,KAAK,YAAU,SAASG,IAAEH,IAAE;AAAC,cAAIE,KAAED,GAAEE,EAAC;AAAE,cAAGD,IAAE;AAAC,gBAAIE,IAAEY,KAAE,EAAE,EAAE,EAAE,EAAE,EAAC,gBAAehB,GAAE,eAAc,GAAEE,GAAE,WAAW,GAAE,YAAU,QAAOE,KAAEF,GAAE,SAAOE,KAAE,CAAC,CAAC,GAAEH,GAAE,gBAAgBC,GAAE,iBAAgBF,EAAC,CAAC,GAAE,EAAC,UAASC,GAAE,UAAS,gBAAeA,GAAE,kBAAgBD,GAAE,SAAQ,SAAQA,GAAE,SAAQ,OAAMA,IAAE,SAAQA,GAAE,QAAO,CAAC;AAAE,mBAAOE,GAAE,OAAOc,EAAC,KAAG;AAAA,UAAI;AAAA,QAAC,GAAE,KAAK,kBAAgB,SAASb,IAAEH,IAAE;AAAC,cAAGG;AAAE,mBAAOA,GAAE,OAAQ,SAASA,IAAEF,IAAE;AAAC,qBAAOE,GAAEF,EAAC,IAAED,GAAEC,EAAC,GAAEE;AAAA,YAAC,GAAG,CAAC,CAAC;AAAA,QAAC,GAAE,KAAK,WAASA,IAAE,KAAK,iBAAeH;AAAA,MAAC;AAAE,UAAI,IAAE,WAAU;AAAC,eAAO,IAAE,OAAO,UAAQ,SAASG,IAAE;AAAC,mBAAQH,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED;AAAI,qBAAQG,MAAKJ,KAAE,UAAUC,EAAC;AAAE,qBAAO,UAAU,eAAe,KAAKD,IAAEI,EAAC,MAAID,GAAEC,EAAC,IAAEJ,GAAEI,EAAC;AAAG,iBAAOD;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC,GAAE,IAAE,SAASA,IAAEH,IAAEC,IAAE;AAAC,YAAGA,MAAG,MAAI,UAAU;AAAO,mBAAQC,IAAEE,KAAE,GAAEY,KAAEhB,GAAE,QAAOI,KAAEY,IAAEZ;AAAI,aAACF,MAAGE,MAAKJ,OAAIE,OAAIA,KAAE,MAAM,UAAU,MAAM,KAAKF,IAAE,GAAEI,EAAC,IAAGF,GAAEE,EAAC,IAAEJ,GAAEI,EAAC;AAAG,eAAOD,GAAE,OAAOD,MAAG,MAAM,UAAU,MAAM,KAAKF,EAAC,CAAC;AAAA,MAAC;AAAE,YAAM,IAAE,SAASG,IAAE;AAAC,YAAIH,KAAEG,GAAE,QAAOC,KAAED,GAAE,gBAAea,KAAEb,GAAE,eAAcE,KAAEF,GAAE,gBAAeG,KAAEH,GAAE,+BAA8BgB,KAAEhB,GAAE,cAAaO,KAAE,SAASP,IAAEH,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,mBAAQC,MAAKC;AAAE,mBAAO,UAAU,eAAe,KAAKA,IAAED,EAAC,KAAGF,GAAE,QAAQE,EAAC,IAAE,MAAID,GAAEC,EAAC,IAAEC,GAAED,EAAC;AAAG,cAAG,QAAMC,MAAG,cAAY,OAAO,OAAO,uBAAsB;AAAC,gBAAIC,KAAE;AAAE,iBAAIF,KAAE,OAAO,sBAAsBC,EAAC,GAAEC,KAAEF,GAAE,QAAOE;AAAI,cAAAJ,GAAE,QAAQE,GAAEE,EAAC,CAAC,IAAE,KAAG,OAAO,UAAU,qBAAqB,KAAKD,IAAED,GAAEE,EAAC,CAAC,MAAIH,GAAEC,GAAEE,EAAC,CAAC,IAAED,GAAED,GAAEE,EAAC,CAAC;AAAA,UAAE;AAAC,iBAAOH;AAAA,QAAC,EAAEE,IAAE,CAAC,UAAS,kBAAiB,iBAAgB,kBAAiB,iCAAgC,cAAc,CAAC,GAAEQ,KAAE,IAAGC,KAAE;AAAG,YAAG,CAACZ,MAAG,CAACI,MAAG,CAACY;AAAE,iBAAM,EAAC,oBAAmBL,KAAE,sHAAqH;AAAE,YAAIS,KAAE,SAASjB,IAAEH,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,iBAAOE,GAAE,mBAAiBF,GAAE,KAAK,sGAAsG,GAAEA;AAAA,QAAC,EAAED,EAAC;AAAE,YAAGoB,GAAE;AAAO,iBAAM,EAAC,mBAAkBR,KAAEQ,GAAE,OAAQ,SAASjB,IAAEH,IAAE;AAAC,mBAAOG,KAAEH;AAAA,UAAC,GAAG,EAAE,EAAC;AAAE,YAAIqB,KAAE,SAASlB,IAAE;AAAC,iBAAOA,GAAE,QAAMA,GAAE,QAAM,CAAC;AAAA,QAAC,EAAEH,EAAC;AAAE,QAAAK,MAAG,MAAM,QAAQA,EAAC,IAAEL,GAAE,kBAAgB,EAAE,CAAC,GAAEK,IAAE,IAAE,IAAE,YAAU,OAAOA,MAAG,QAAQA,EAAC,MAAIC,OAAIN,GAAE,kBAAgB,CAAC;AAAI,YAAIsB,IAAET,IAAEC,IAAEC,KAAE,EAAE,EAAE,SAAS,EAAE,EAAC,UAAS,EAAE,CAAC,GAAEf,GAAE,iBAAgB,IAAE,EAAC,GAAEqB,EAAC,CAAC,GAAEJ,KAAEF,GAAE,CAAC,GAAEG,KAAEH,GAAE,CAAC,GAAEQ,KAAE,EAAE,EAAE,OAAON,GAAE,QAAQ,GAAEO,KAAE,EAAE,EAAE,OAAO,GAAEG,KAAE,EAAE,EAAE,OAAO;AAAE,SAAC,GAAE,EAAE,WAAY,WAAU;AAAC,UAAAJ,GAAE,UAAQN,GAAE;AAAA,QAAQ,CAAE,IAAG,GAAE,EAAE,WAAY,WAAU;AAAC,UAAAZ,MAAG,MAAM,QAAQA,EAAC,KAAGa,GAAG,SAASf,IAAE;AAAC,mBAAO,EAAE,EAAE,CAAC,GAAEA,EAAC,GAAE,EAAC,UAASE,GAAC,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC,GAAG,CAAC,CAAC,IAAG,GAAE,EAAE,WAAY,WAAU;AAAC,cAAIF,KAAEwB,GAAE;AAAQ,iBAAO,WAAU;AAAC,gBAAGR,MAAG,cAAY,OAAOA,IAAE;AAAC,kBAAInB,KAAEG,GAAE,UAAU,SAAS;AAAE,cAAAgB,GAAEI,GAAE,SAAQvB,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAG,CAAC,CAAC,IAAG,GAAE,EAAE,WAAY,WAAU;AAAC,UAAAwB,GAAE,UAAQP;AAAA,QAAC,GAAG,CAACA,EAAC,CAAC;AAAE,YAAIc,KAAE3B,IAAE4B,KAAEhB;AAAE,eAAO,EAAEe,EAAC,KAAG,EAAEC,EAAC,KAAGV,KAAE,IAAIlB,GAAE,GAAEc,IAAE,GAAEM,GAAE,SAAQ,GAAEd,EAAC,GAAEG,KAAE,IAAI,EAAEK,IAAEI,EAAC,GAAER,KAAE,IAAIE,GAAEM,IAAEE,GAAE,OAAO,GAAE,EAAExB,EAAC,EAAE,QAAS,SAASG,IAAE;AAAC,iBAAO,QAAMU,KAAE,SAAOA,GAAE,UAAUV,IAAEO,EAAC;AAAA,QAAC,CAAE,MAAIY,KAAElB,IAAEU,KAAEE,IAAEH,KAAE,IAAI,EAAEK,IAAE,IAAI,GAAE,EAAElB,EAAC,EAAE,QAAS,SAASG,IAAE;AAAC,iBAAO,QAAMU,KAAE,SAAOA,GAAE,UAAUV,IAAEO,EAAC;AAAA,QAAC,CAAE,IAAG,EAAC,gBAAeG,IAAE,YAAWS,IAAE,aAAYR,IAAE,oBAAmBH,IAAE,mBAAkBC,IAAE,OAAMK,IAAE,UAASC,IAAE,qBAAoBS,IAAE,gBAAeI,IAAE,eAAcC,GAAC;AAAA,MAAC;AAAE,UAAI,IAAE,WAAU;AAAC,eAAO,IAAE,OAAO,UAAQ,SAAS7B,IAAE;AAAC,mBAAQH,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED;AAAI,qBAAQG,MAAKJ,KAAE,UAAUC,EAAC;AAAE,qBAAO,UAAU,eAAe,KAAKD,IAAEI,EAAC,MAAID,GAAEC,EAAC,IAAEJ,GAAEI,EAAC;AAAG,iBAAOD;AAAA,QAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,MAAC;AAAE,YAAM,IAAE,SAASA,IAAE;AAAC,YAAIH,KAAEG,GAAE,gBAAeF,KAAEE,GAAE,eAAcC,KAAED,GAAE,QAAOa,KAAEb,GAAE,YAAWE,KAAEF,GAAE,iBAAgBG,KAAEH,GAAE,cAAaK,KAAEL,GAAE,gBAAeM,KAAEN,GAAE,+BAA8BgB,KAAEhB,GAAE,uBAAsBO,KAAEP,GAAE,WAAUQ,KAAE,SAASR,IAAEH,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,mBAAQC,MAAKC;AAAE,mBAAO,UAAU,eAAe,KAAKA,IAAED,EAAC,KAAGF,GAAE,QAAQE,EAAC,IAAE,MAAID,GAAEC,EAAC,IAAEC,GAAED,EAAC;AAAG,cAAG,QAAMC,MAAG,cAAY,OAAO,OAAO,uBAAsB;AAAC,gBAAIC,KAAE;AAAE,iBAAIF,KAAE,OAAO,sBAAsBC,EAAC,GAAEC,KAAEF,GAAE,QAAOE;AAAI,cAAAJ,GAAE,QAAQE,GAAEE,EAAC,CAAC,IAAE,KAAG,OAAO,UAAU,qBAAqB,KAAKD,IAAED,GAAEE,EAAC,CAAC,MAAIH,GAAEC,GAAEE,EAAC,CAAC,IAAED,GAAED,GAAEE,EAAC,CAAC;AAAA,UAAE;AAAC,iBAAOH;AAAA,QAAC,EAAEE,IAAE,CAAC,kBAAiB,iBAAgB,UAAS,cAAa,mBAAkB,gBAAe,kBAAiB,iCAAgC,yBAAwB,WAAW,CAAC,GAAES,KAAE,EAAE,EAAE,EAAC,QAAOR,IAAE,gBAAeJ,IAAE,eAAcC,IAAE,gBAAeO,IAAE,cAAaF,IAAE,+BAA8BG,GAAC,GAAEE,EAAC,CAAC,GAAES,KAAER,GAAE,oBAAmBS,KAAET,GAAE,mBAAkBU,KAAEV,GAAE,gBAAeC,KAAED,GAAE,eAAcE,KAAEF,GAAE,gBAAeG,KAAEH,GAAE,qBAAoBK,KAAEL,GAAE,YAAWM,KAAEN,GAAE,aAAYa,KAAEb,GAAE,OAAMe,KAAEf,GAAE;AAAS,YAAGQ;AAAE,iBAAO,EAAE,EAAE,cAAc,GAAE,EAAC,SAAQA,GAAC,CAAC;AAAE,YAAGC,GAAE;AAAO,iBAAO,EAAE,EAAE,cAAc,GAAE,EAAC,SAAQA,GAAC,CAAC;AAAE,YAAIO,KAAE,SAASzB,IAAE;AAAC,iBAAOA,GAAE,eAAaA,GAAE,eAAa,CAAC;AAAA,QAAC,EAAEC,EAAC,GAAEyB,KAAE,SAAS1B,IAAE;AAAC,iBAAOA,GAAE,mBAAiBA,GAAE,mBAAiB,CAAC;AAAA,QAAC,EAAEC,EAAC,GAAE0B,KAAE,SAAS3B,IAAE;AAAC,iBAAOA,GAAE,UAAQA,GAAE,UAAQ;AAAA,QAAK,EAAEC,EAAC,GAAE6B,KAAE,SAAS9B,IAAE;AAAC,iBAAOA,GAAE,iBAAeA,GAAE,iBAAe,CAAC;AAAA,QAAC,EAAEC,EAAC;AAAE,eAAO,EAAEkB,EAAC,KAAG,EAAET,EAAC,IAAE,EAAE,EAAE,cAAc,GAAE,EAAC,OAAMY,IAAE,UAASE,IAAE,gBAAeb,IAAE,gBAAeG,IAAE,eAAcC,IAAE,gBAAee,IAAE,kBAAiB,EAAE,CAAC,GAAEJ,EAAC,GAAE,SAAQC,IAAE,cAAa,EAAE,CAAC,GAAEF,EAAC,GAAE,YAAWZ,IAAE,iBAAgBX,IAAE,WAAUK,IAAE,gBAAeF,IAAE,uBAAsBW,IAAE,qBAAoBJ,GAAC,CAAC,IAAE,EAAE,EAAE,cAAcO,IAAE,EAAC,OAAMG,IAAE,UAASE,IAAE,sBAAqB,EAAC,GAAE,EAAE,EAAE,cAAcd,IAAE,MAAK,EAAE,EAAE,cAAc,GAAE,EAAC,OAAMY,IAAE,UAASE,IAAE,gBAAeb,IAAE,gBAAeQ,IAAE,eAAcT,IAAE,gBAAeoB,IAAE,kBAAiB,EAAE,CAAC,GAAEJ,EAAC,GAAE,SAAQC,IAAE,cAAa,EAAE,CAAC,GAAEF,EAAC,GAAE,YAAWZ,IAAE,iBAAgBX,IAAE,WAAUK,IAAE,gBAAeF,IAAE,uBAAsBW,IAAE,qBAAoBJ,GAAC,CAAC,CAAC,CAAC;AAAA,MAAC,GAAE,IAAE;AAAE,aAAO,UAAQ;AAAA,IAAC,GAAG;AAAA;AAAA;", "names": ["o", "r", "e", "t", "r", "a", "e", "n", "s", "c", "i", "l", "u", "g", "d", "f", "y", "b", "w", "o", "E", "P", "m", "h", "p", "v", "S", "O", "k", "M", "C", "N", "x", "j", "T", "A", "B", "H"]}