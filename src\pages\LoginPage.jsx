import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { LogIn, Mail, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/use-toast';

const LoginPage = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    setTimeout(() => {
      if (email === '<EMAIL>' && password === 'password') {
        login({ email, name: 'Test Farmer', type: 'farmer' });
        toast({ title: t('loginSuccessTitle', 'Login Successful!'), description: t('loginSuccessFarmerDesc', 'Welcome back, Farmer!') });
        navigate('/farmer-dashboard');
      } else if (email === '<EMAIL>' && password === 'password') {
        login({ email, name: 'Test Buyer', type: 'buyer' });
        toast({ title: t('loginSuccessTitle', 'Login Successful!'), description: t('loginSuccessBuyerDesc', 'Welcome back, Buyer!') });
        navigate('/buyer-dashboard');
      } else {
        toast({ title: t('loginErrorTitle', 'Login Failed'), description: t('loginErrorDesc', 'Invalid email or password.'), variant: 'destructive' });
      }
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-100 flex flex-col">
      <Navigation />
      <div className="flex-grow flex items-center justify-center p-4 pt-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="w-full max-w-md shadow-2xl glass-effect">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mb-4">
                <LogIn className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-3xl font-bold gradient-text">{t('login', 'Login')}</CardTitle>
              <CardDescription>{t('loginPrompt', 'Access your Kivu SMARTFARM account')}</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="email"
                      type="email"
                      placeholder={t('emailPlaceholder', 'Enter your email')}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="pl-10 h-12 text-base"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="password"
                      type="password"
                      placeholder={t('passwordPlaceholder', 'Enter your password')}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="pl-10 h-12 text-base"
                    />
                  </div>
                  <div className="flex items-center justify-end">
                    <Link to="/forgot-password" className="text-sm text-green-600 hover:underline">
                      {t('forgotPassword', 'Forgot password?')}
                    </Link>
                  </div>
                </div>
                <Button type="submit" className="w-full gradient-bg h-12 text-lg" disabled={isLoading}>
                  {isLoading ? t('loading', 'Loading...') : t('login', 'Login')}
                </Button>
              </form>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  {t('noAccount', "Don't have an account?")}{' '}
                  <Link to="/signup" className="font-medium text-green-600 hover:underline">
                    {t('signup', 'Sign up')}
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default LoginPage;