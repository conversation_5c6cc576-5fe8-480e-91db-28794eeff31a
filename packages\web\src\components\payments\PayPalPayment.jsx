import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { DollarSign, ExternalLink, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@kivu-smartfarm/shared';

const PayPalPayment = ({ amount, onSuccess, onError }) => {
  const { t } = useLanguage();
  const [loading, setLoading] = useState(false);

  const handlePayPalPayment = async () => {
    setLoading(true);

    try {
      // Simulate PayPal redirect and payment processing
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simulate PayPal popup/redirect
      const paypalWindow = window.open(
        'about:blank',
        'paypal',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );
      
      if (paypalWindow) {
        paypalWindow.document.write(`
          <html>
            <head>
              <title>PayPal - Secure Payment</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  background: #f8f9fa;
                  margin: 0;
                  padding: 20px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  min-height: 100vh;
                }
                .paypal-container {
                  background: white;
                  padding: 30px;
                  border-radius: 8px;
                  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                  text-align: center;
                  max-width: 400px;
                }
                .paypal-logo {
                  color: #003087;
                  font-size: 24px;
                  font-weight: bold;
                  margin-bottom: 20px;
                }
                .amount {
                  font-size: 32px;
                  font-weight: bold;
                  color: #333;
                  margin: 20px 0;
                }
                .merchant {
                  color: #666;
                  margin-bottom: 30px;
                }
                .pay-button {
                  background: #0070ba;
                  color: white;
                  border: none;
                  padding: 12px 30px;
                  border-radius: 25px;
                  font-size: 16px;
                  cursor: pointer;
                  margin: 10px;
                  min-width: 120px;
                }
                .pay-button:hover {
                  background: #005ea6;
                }
                .cancel-button {
                  background: #666;
                  color: white;
                  border: none;
                  padding: 12px 30px;
                  border-radius: 25px;
                  font-size: 16px;
                  cursor: pointer;
                  margin: 10px;
                  min-width: 120px;
                }
                .security-info {
                  margin-top: 20px;
                  font-size: 12px;
                  color: #666;
                }
                .loading {
                  display: none;
                  margin-top: 20px;
                }
                .loading.show {
                  display: block;
                }
                .spinner {
                  border: 2px solid #f3f3f3;
                  border-top: 2px solid #0070ba;
                  border-radius: 50%;
                  width: 20px;
                  height: 20px;
                  animation: spin 1s linear infinite;
                  margin: 0 auto;
                }
                @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
                }
              </style>
            </head>
            <body>
              <div class="paypal-container">
                <div class="paypal-logo">PayPal</div>
                <div class="merchant">Payment to Kivu SmartFarm</div>
                <div class="amount">$${amount?.toFixed(2)}</div>
                <div>
                  <button class="pay-button" onclick="processPayment()">Pay Now</button>
                  <button class="cancel-button" onclick="window.close()">Cancel</button>
                </div>
                <div class="loading" id="loading">
                  <div class="spinner"></div>
                  <p>Processing your payment...</p>
                </div>
                <div class="security-info">
                  🔒 Your payment is protected by PayPal's security
                </div>
              </div>
              
              <script>
                function processPayment() {
                  document.getElementById('loading').classList.add('show');
                  document.querySelector('.pay-button').style.display = 'none';
                  document.querySelector('.cancel-button').style.display = 'none';
                  
                  setTimeout(() => {
                    // Simulate payment success
                    if (Math.random() > 0.05) {
                      window.opener.postMessage({
                        type: 'PAYPAL_SUCCESS',
                        data: {
                          transactionId: 'pp_' + Date.now(),
                          amount: ${amount}
                        }
                      }, '*');
                    } else {
                      window.opener.postMessage({
                        type: 'PAYPAL_ERROR',
                        error: 'Payment failed. Please try again.'
                      }, '*');
                    }
                    window.close();
                  }, 2000);
                }
              </script>
            </body>
          </html>
        `);
      }

      // Listen for messages from PayPal popup
      const handleMessage = (event) => {
        if (event.data.type === 'PAYPAL_SUCCESS') {
          window.removeEventListener('message', handleMessage);
          onSuccess(event.data.data);
        } else if (event.data.type === 'PAYPAL_ERROR') {
          window.removeEventListener('message', handleMessage);
          onError(event.data.error);
        }
        setLoading(false);
      };

      window.addEventListener('message', handleMessage);

      // Handle popup being closed without completion
      const checkClosed = setInterval(() => {
        if (paypalWindow.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          setLoading(false);
          onError('Payment was cancelled');
        }
      }, 1000);

    } catch (error) {
      onError(error.message);
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <DollarSign className="w-5 h-5 mr-2 text-blue-600" />
          {t('payment.paypal', 'PayPal')}
        </CardTitle>
        <CardDescription>
          {t('payment.paypal_description', 'Pay with your PayPal account or credit card')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-start space-x-2">
            <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">
                {t('payment.paypal_benefits', 'PayPal Benefits')}
              </h4>
              <ul className="text-sm text-blue-800 mt-2 space-y-1">
                <li>• {t('payment.paypal_benefit1', 'Buyer protection on eligible purchases')}</li>
                <li>• {t('payment.paypal_benefit2', 'No need to share card details')}</li>
                <li>• {t('payment.paypal_benefit3', 'Pay with PayPal balance or linked cards')}</li>
                <li>• {t('payment.paypal_benefit4', 'Instant payment confirmation')}</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-4">
            <span className="font-medium">{t('payment.amount_to_pay', 'Amount to Pay')}</span>
            <span className="text-xl font-bold text-green-600">
              ${amount?.toFixed(2)}
            </span>
          </div>

          <Button
            onClick={handlePayPalPayment}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700"
            size="lg"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('payment.opening_paypal', 'Opening PayPal...')}
              </div>
            ) : (
              <div className="flex items-center">
                <DollarSign className="w-4 h-4 mr-2" />
                {t('payment.pay_with_paypal', 'Pay with PayPal')}
                <ExternalLink className="w-4 h-4 ml-2" />
              </div>
            )}
          </Button>
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            {t('payment.paypal_redirect', 'You will be redirected to PayPal to complete your payment')}
          </p>
        </div>

        {loading && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-yellow-50 p-4 rounded-lg"
          >
            <div className="flex items-center space-x-2">
              <div className="animate-pulse w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-yellow-800 text-sm">
                {t('payment.paypal_popup', 'Please complete your payment in the PayPal popup window')}
              </span>
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
};

export default PayPalPayment;
