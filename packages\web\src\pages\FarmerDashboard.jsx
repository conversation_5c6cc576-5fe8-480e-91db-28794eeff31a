import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { BarChart3, TrendingUp, Package, AlertCircle, Camera, Plus, Eye, ShoppingBasket } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Navigation from '@/components/Navigation';
import { useLanguage, useAuth } from '@kivu-smartfarm/shared';

const FarmerDashboard = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [crops, setCrops] = useState([]);
  const [analytics, setAnalytics] = useState({
    totalCrops: 0,
    activeListing: 0,
    monthlyRevenue: 0,
    analysisCount: 0
  });

  useEffect(() => {
    const storedCrops = localStorage.getItem('farmerCrops');
    if (storedCrops) {
      const cropsData = JSON.parse(storedCrops);
      setCrops(cropsData);
      
      setAnalytics({
        totalCrops: cropsData.length,
        activeListing: cropsData.filter(crop => crop.status === 'active').length,
        monthlyRevenue: cropsData.reduce((sum, crop) => sum + (crop.revenue || 0), 0),
        analysisCount: cropsData.filter(crop => crop.aiAnalysis).length
      });
    }
  }, []);

  const addNewCrop = () => {
    const newCrop = {
      id: Date.now(),
      name: 'New Crop',
      type: 'vegetables',
      quantity: 100,
      price: 50,
      status: 'active',
      plantedDate: new Date().toISOString().split('T')[0],
      expectedHarvest: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      location: 'Kivu Region',
      revenue: 0
    };
    
    const updatedCrops = [...crops, newCrop];
    setCrops(updatedCrops);
    localStorage.setItem('farmerCrops', JSON.stringify(updatedCrops));
  };

  const quickStats = [
    {
      title: 'Total Crops',
      value: analytics.totalCrops,
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Active Listings',
      value: analytics.activeListing,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Monthly Revenue',
      value: `${analytics.monthlyRevenue}`,
      icon: BarChart3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'AI Analysis',
      value: analytics.analysisCount,
      icon: Eye,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      <Navigation />
      
      <div className="pt-20 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <h1 className="text-3xl font-bold gradient-text mb-2">
              Welcome back, {user?.name || 'Farmer'}!
            </h1>
            <p className="text-gray-600">
              Manage your crops, track performance, and grow your agricultural business.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {quickStats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="card-hover">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 mb-1">{stat.title}</p>
                        <p className="text-2xl font-bold">{stat.value}</p>
                      </div>
                      <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                        <stat.icon className={`w-6 h-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>My Crops</CardTitle>
                    <CardDescription>
                      Manage your crop listings and track their performance
                    </CardDescription>
                  </div>
                  <Button onClick={addNewCrop} className="gradient-bg">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Crop
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {crops.length === 0 ? (
                      <div className="text-center py-8">
                        <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4">No crops added yet</p>
                        <Button onClick={addNewCrop} variant="outline">
                          Add Your First Crop
                        </Button>
                      </div>
                    ) : (
                      crops.map((crop) => (
                        <motion.div
                          key={crop.id}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <img  
                                className="w-12 h-12 rounded-lg object-cover" 
                                alt={`${crop.name} crop`}
                               src="https://images.unsplash.com/photo-1573387983727-203a0d2ba36e" />
                              <div>
                                <h3 className="font-semibold">{crop.name}</h3>
                                <p className="text-sm text-gray-600">{crop.location}</p>
                              </div>
                            </div>
                            <Badge variant={crop.status === 'active' ? 'default' : 'secondary'}>
                              {crop.status}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600">Quantity</p>
                              <p className="font-semibold">{crop.quantity} kg</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Price</p>
                              <p className="font-semibold">${crop.price}/kg</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Planted</p>
                              <p className="font-semibold">{crop.plantedDate}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Harvest</p>
                              <p className="font-semibold">{crop.expectedHarvest}</p>
                            </div>
                          </div>
                          <div className="mt-4 flex justify-end">
                            <Link to={`/product/${crop.id}`}>
                              <Button variant="outline" size="sm">View Details</Button>
                            </Link>
                          </div>
                        </motion.div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card className="card-hover">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Camera className="w-5 h-5 mr-2 text-purple-600" />
                    AI Crop Analysis
                  </CardTitle>
                  <CardDescription>
                    Upload crop images for instant health analysis
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full" variant="outline" onClick={() => navigate('/ai-analysis')}>
                    <Camera className="w-4 h-4 mr-2" />
                    Analyze Crop
                  </Button>
                </CardContent>
              </Card>

              <Card className="card-hover">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ShoppingBasket className="w-5 h-5 mr-2 text-blue-600" />
                    Buyer Demand Portal
                  </CardTitle>
                  <CardDescription>
                    View real-time demand from major buyers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full" variant="outline" onClick={() => navigate('/demand-portal')}>
                    <Eye className="w-4 h-4 mr-2" />
                    View Demand
                  </Button>
                </CardContent>
              </Card>

              <Card className="card-hover">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
                    Market Insights
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Tomatoes</span>
                    <span className="text-sm font-semibold text-green-600">↑ $2.50/kg</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Maize</span>
                    <span className="text-sm font-semibold text-red-600">↓ $1.80/kg</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Beans</span>
                    <span className="text-sm font-semibold text-green-600">↑ $3.20/kg</span>
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-3">
                    View Full Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="card-hover">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <AlertCircle className="w-5 h-5 mr-2 text-orange-600" />
                    Alerts & Notifications
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <p className="text-sm font-medium text-orange-800">Weather Alert</p>
                    <p className="text-xs text-orange-600">Heavy rain expected this week</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm font-medium text-blue-800">New Buyer Interest</p>
                    <p className="text-xs text-blue-600">3 buyers interested in your tomatoes</p>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm font-medium text-green-800">Harvest Reminder</p>
                    <p className="text-xs text-green-600">Maize ready for harvest in 5 days</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FarmerDashboard;