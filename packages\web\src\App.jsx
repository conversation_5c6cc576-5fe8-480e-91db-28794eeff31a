import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';
import { LanguageProvider, AuthProvider, CartProvider } from '@kivu-smartfarm/shared';
import HomePage from '@/pages/HomePage';
import FarmerDashboard from '@/pages/FarmerDashboard';
import BuyerDashboard from '@/pages/BuyerDashboard';
import MarketPlace from '@/pages/MarketPlace';
import AIAnalysis from '@/pages/AIAnalysis';
import Logistics from '@/pages/Logistics';
import Subscription from '@/pages/Subscription';
import LoginPage from '@/pages/LoginPage';
import SignupPage from '@/pages/SignupPage';
import ProductDetailsPage from '@/pages/ProductDetailsPage';
import ChatbotWidget from '@/components/ChatbotWidget';
import DemandPortal from '@/pages/DemandPortal';

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <CartProvider>
          <Router>
            <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/farmer-dashboard" element={<FarmerDashboard />} />
                <Route path="/buyer-dashboard" element={<BuyerDashboard />} />
                <Route path="/marketplace" element={<MarketPlace />} />
                <Route path="/product/:productId" element={<ProductDetailsPage />} />
                <Route path="/ai-analysis" element={<AIAnalysis />} />
                <Route path="/logistics" element={<Logistics />} />
                <Route path="/subscription" element={<Subscription />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/signup" element={<SignupPage />} />
                <Route path="/demand-portal" element={<DemandPortal />} />
              </Routes>
              <Toaster />
              <ChatbotWidget />
            </div>
          </Router>
        </CartProvider>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;