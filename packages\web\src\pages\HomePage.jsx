import React from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowRight, Users, TrendingUp, Eye, Truck, Smartphone, Shield, BarChart3, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@kivu-smartfarm/shared';

const HomePage = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  const features = [
    {
      icon: Users,
      title: t('marketLinkage'),
      description: t('marketLinkageDesc'),
      color: 'from-blue-500 to-blue-600',
      link: '/marketplace'
    },
    {
      icon: Smartphone,
      title: t('aiVision'),
      description: t('aiVisionDesc'),
      color: 'from-purple-500 to-purple-600',
      link: '/ai-analysis'
    },
    {
      icon: Shield,
      title: t('supplyChain'),
      description: t('supplyChainDesc'),
      color: 'from-green-500 to-green-600',
      link: '/quality-tracking' 
    },
    {
      icon: Truck,
      title: t('logistics'),
      description: t('logisticsDesc'),
      color: 'from-orange-500 to-orange-600',
      link: '/logistics'
    }
  ];

  const stats = [
    { label: t('farmersConnected'), value: '2,500+', icon: Users },
    { label: t('buyersActive'), value: '850+', icon: TrendingUp },
    { label: t('cropsAnalyzed'), value: '15,000+', icon: Eye },
    { label: t('successRate'), value: '94%', icon: BarChart3 }
  ];

  return (
    <div className="min-h-screen">
      <Navigation />
      
      <section className="relative pt-20 pb-16 overflow-hidden hero-pattern">
        <div className="absolute inset-0 bg-gradient-to-br from-green-50/80 via-white/60 to-green-100/80"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                  <span className="gradient-text">{t('heroTitle')}</span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  {t('heroSubtitle')}
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="gradient-bg text-lg px-8 py-4" onClick={() => navigate('/signup')}>
                  {t('getStarted')}
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
                <Button variant="outline" size="lg" className="text-lg px-8 py-4" onClick={() => navigate('/marketplace')}>
                  {t('learnMore')}
                </Button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative z-10">
                <img  
                  className="w-full h-auto rounded-2xl shadow-2xl animate-float" 
                  alt="Modern farming technology in Democratic Republic of Congo"
                 src="https://images.unsplash.com/photo-1554048807-b043cffa8118" />
              </div>
              <div className="absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-4 -left-4 w-72 h-72 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <Card className="stats-card card-hover">
                  <CardContent className="p-6">
                    <stat.icon className="w-8 h-8 mx-auto mb-4 text-green-600" />
                    <div className="text-3xl font-bold text-green-600 mb-2">{stat.value}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-br from-gray-50 to-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold gradient-text mb-4">{t('featuresTitle')}</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover how our innovative platform is revolutionizing agriculture in the Kivu region
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -10 }}
                className="group"
              >
                <Link to={feature.link}>
                  <Card className="feature-card card-hover h-full">
                    <CardHeader className="text-center">
                      <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <feature.icon className="w-8 h-8 text-white" />
                      </div>
                      <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center text-gray-600">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold gradient-text mb-4">
              {t('marketplace.title', 'Explore Our Marketplace')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('marketplace.subtitle', 'Find fresh, high-quality produce directly from local farmers.')}
            </p>
          </motion.div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { name: 'Fresh Tomatoes', image: 'Fresh red tomatoes in agricultural field', price: '$2.50/kg', link: '/product/1' },
              { name: 'Sweet Maize', image: 'Golden yellow maize corn in farming field', price: '$1.80/kg', link: '/product/2' },
              { name: 'Organic Green Beans', image: 'Fresh green beans growing in organic farm', price: '$3.20/kg', link: '/product/3' },
            ].map((product, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <Link to={product.link}>
                  <Card className="card-hover h-full overflow-hidden">
                    <img 
                      className="w-full h-48 object-cover"
                      alt={product.image}
                     src="https://images.unsplash.com/photo-1635865165118-917ed9e20936" />
                    <CardContent className="p-4">
                      <CardTitle className="text-lg mb-1">{product.name}</CardTitle>
                      <p className="text-green-600 font-semibold mb-2">{product.price}</p>
                      <Button variant="outline" size="sm" className="w-full">
                        <ShoppingBag className="w-4 h-4 mr-2" />
                        {t('viewProduct', 'View Product')}
                      </Button>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" className="gradient-bg" onClick={() => navigate('/marketplace')}>
              {t('exploreMarketplace', 'Explore Full Marketplace')}
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      <section className="py-20 gradient-bg">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Transform Your Agricultural Journey?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Join thousands of farmers and buyers who are already benefiting from our platform
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-4" onClick={() => navigate('/signup?role=farmer')}>
                Start as Farmer
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8 py-4 border-white text-black bg-white hover:bg-gray-100 hover:text-black" onClick={() => navigate('/signup?role=buyer')}>
                Join as Buyer
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">KS</span>
                </div>
                <span className="font-bold text-xl">Kivu SMARTFARM</span>
              </Link>
              <p className="text-gray-400">
                Transforming agriculture in the Democratic Republic of Congo through innovation and technology.
              </p>
            </div>
            
            <div>
              <span className="font-semibold text-lg mb-4 block">Platform</span>
              <div className="space-y-2">
                <Link to="/marketplace" className="text-gray-400 hover:text-white">Marketplace</Link><br/>
                <Link to="/ai-analysis" className="text-gray-400 hover:text-white">AI Analysis</Link><br/>
                <Link to="/logistics" className="text-gray-400 hover:text-white">Logistics</Link><br/>
                <Link to="/quality-tracking" className="text-gray-400 hover:text-white">Quality Tracking</Link>
              </div>
            </div>
            
            <div>
              <span className="font-semibold text-lg mb-4 block">Support</span>
              <div className="space-y-2">
                <Link to="/help" className="text-gray-400 hover:text-white">Help Center</Link><br/>
                <Link to="/docs" className="text-gray-400 hover:text-white">Documentation</Link><br/>
                <Link to="/training" className="text-gray-400 hover:text-white">Training</Link><br/>
                <Link to="/contact" className="text-gray-400 hover:text-white">Contact Us</Link>
              </div>
            </div>
            
            <div>
              <span className="font-semibold text-lg mb-4 block">Company</span>
              <div className="space-y-2">
                <Link to="/about" className="text-gray-400 hover:text-white">About Us</Link><br/>
                <Link to="/careers" className="text-gray-400 hover:text-white">Careers</Link><br/>
                <Link to="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link><br/>
                <Link to="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} Kivu SMARTFARM. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;