
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Camera, Upload, <PERSON>an, Al<PERSON><PERSON>riangle, CheckCircle, Info, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { toast } from '@/components/ui/use-toast';

const AIAnalysis = () => {
  const { t } = useLanguage();
  const [selectedImage, setSelectedImage] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [analysisHistory, setAnalysisHistory] = useState([
    {
      id: 1,
      cropName: 'Tomato Plant',
      date: '2024-01-15',
      status: 'healthy',
      confidence: 94,
      issues: [],
      recommendations: ['Continue current care routine', 'Monitor for seasonal changes']
    },
    {
      id: 2,
      cropName: 'Maize Crop',
      date: '2024-01-12',
      status: 'warning',
      confidence: 87,
      issues: ['Early blight detected', 'Nutrient deficiency signs'],
      recommendations: ['Apply fungicide treatment', 'Increase nitrogen fertilizer', 'Improve drainage']
    },
    {
      id: 3,
      cropName: 'Bean Plants',
      date: '2024-01-10',
      status: 'critical',
      confidence: 92,
      issues: ['Severe pest infestation', 'Leaf curl virus'],
      recommendations: ['Immediate pest control treatment', 'Remove infected plants', 'Apply organic pesticide']
    }
  ]);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeImage = async () => {
    if (!selectedImage) {
      toast({
        title: "No Image Selected",
        description: "Please upload an image first.",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);
    
    // Simulate AI analysis
    setTimeout(() => {
      const mockResults = [
        {
          cropName: 'Tomato Plant',
          status: 'healthy',
          confidence: 96,
          issues: [],
          recommendations: [
            'Plant appears healthy with good growth patterns',
            'Continue current watering schedule',
            'Monitor for seasonal pest activity'
          ],
          details: {
            healthScore: 96,
            growthStage: 'Flowering',
            estimatedYield: 'High',
            diseases: 'None detected',
            pests: 'None detected',
            nutritionalStatus: 'Optimal'
          }
        },
        {
          cropName: 'Maize Plant',
          status: 'warning',
          confidence: 89,
          issues: ['Nitrogen deficiency signs', 'Minor leaf spot'],
          recommendations: [
            'Apply nitrogen-rich fertilizer within 3-5 days',
            'Increase watering frequency slightly',
            'Monitor leaf spot progression',
            'Consider organic fungicide if spots spread'
          ],
          details: {
            healthScore: 78,
            growthStage: 'Vegetative',
            estimatedYield: 'Medium',
            diseases: 'Minor leaf spot detected',
            pests: 'None detected',
            nutritionalStatus: 'Nitrogen deficient'
          }
        },
        {
          cropName: 'Bean Plant',
          status: 'critical',
          confidence: 94,
          issues: ['Aphid infestation', 'Bacterial blight', 'Severe nutrient stress'],
          recommendations: [
            'URGENT: Apply insecticidal soap for aphids',
            'Remove and destroy infected leaves immediately',
            'Apply copper-based bactericide',
            'Improve soil drainage',
            'Apply balanced fertilizer after treatment'
          ],
          details: {
            healthScore: 34,
            growthStage: 'Stressed',
            estimatedYield: 'Low',
            diseases: 'Bacterial blight confirmed',
            pests: 'Heavy aphid infestation',
            nutritionalStatus: 'Severely deficient'
          }
        }
      ];

      const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)];
      setAnalysisResult(randomResult);
      
      // Add to history
      const newHistoryItem = {
        id: Date.now(),
        ...randomResult,
        date: new Date().toISOString().split('T')[0]
      };
      setAnalysisHistory(prev => [newHistoryItem, ...prev]);
      
      setIsAnalyzing(false);
      
      toast({
        title: "Analysis Complete",
        description: `Your ${randomResult.cropName} has been analyzed successfully.`
      });
    }, 3000);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'critical': return AlertTriangle;
      default: return Info;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      <Navigation />
      
      <div className="pt-20 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <h1 className="text-4xl font-bold gradient-text mb-4">
              AI Crop Analysis
            </h1>
            <p className="text-xl text-gray-600">
              Upload crop images for instant health analysis, disease detection, and treatment recommendations
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Upload and Analysis Section */}
            <div className="lg:col-span-2 space-y-6">
              {/* Image Upload */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Camera className="w-6 h-6 mr-2 text-purple-600" />
                      Upload Crop Image
                    </CardTitle>
                    <CardDescription>
                      Take a clear photo of your crop for AI-powered analysis
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {!selectedImage ? (
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
                          <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 mb-4">
                            Drag and drop an image here, or click to select
                          </p>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="hidden"
                            id="image-upload"
                          />
                          <label htmlFor="image-upload">
                            <Button variant="outline" className="cursor-pointer">
                              <Upload className="w-4 h-4 mr-2" />
                              Choose Image
                            </Button>
                          </label>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="relative">
                            <img
                              src={selectedImage}
                              alt="Selected crop"
                              className="w-full h-64 object-cover rounded-lg"
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              className="absolute top-2 right-2 bg-white/90"
                              onClick={() => setSelectedImage(null)}
                            >
                              Remove
                            </Button>
                          </div>
                          
                          <Button
                            onClick={analyzeImage}
                            disabled={isAnalyzing}
                            className="w-full gradient-bg h-12 text-lg"
                          >
                            {isAnalyzing ? (
                              <>
                                <Scan className="w-5 h-5 mr-2 animate-spin" />
                                Analyzing...
                              </>
                            ) : (
                              <>
                                <Scan className="w-5 h-5 mr-2" />
                                Analyze Crop
                              </>
                            )}
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Analysis Results */}
              {analysisResult && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center">
                          <Scan className="w-6 h-6 mr-2 text-blue-600" />
                          Analysis Results
                        </CardTitle>
                        <Badge className={getStatusColor(analysisResult.status)}>
                          {analysisResult.status.toUpperCase()}
                        </Badge>
                      </div>
                      <CardDescription>
                        AI confidence: {analysisResult.confidence}%
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Crop Details */}
                      <div>
                        <h3 className="font-semibold text-lg mb-3">{analysisResult.cropName}</h3>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Health Score</p>
                            <p className="font-semibold text-lg">{analysisResult.details.healthScore}%</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Growth Stage</p>
                            <p className="font-semibold">{analysisResult.details.growthStage}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Estimated Yield</p>
                            <p className="font-semibold">{analysisResult.details.estimatedYield}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Diseases</p>
                            <p className="font-semibold">{analysisResult.details.diseases}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Pests</p>
                            <p className="font-semibold">{analysisResult.details.pests}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Nutrition</p>
                            <p className="font-semibold">{analysisResult.details.nutritionalStatus}</p>
                          </div>
                        </div>
                      </div>

                      {/* Issues */}
                      {analysisResult.issues.length > 0 && (
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center">
                            <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
                            Issues Detected
                          </h4>
                          <div className="space-y-2">
                            {analysisResult.issues.map((issue, index) => (
                              <div key={index} className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                                <p className="text-orange-800 font-medium">{issue}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Recommendations */}
                      <div>
                        <h4 className="font-semibold mb-3 flex items-center">
                          <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
                          Recommendations
                        </h4>
                        <div className="space-y-2">
                          {analysisResult.recommendations.map((rec, index) => (
                            <div key={index} className="p-3 bg-green-50 rounded-lg border border-green-200">
                              <p className="text-green-800">{rec}</p>
                            </div>
                          ))}
                        </div>
                      </div>

                      <Button variant="outline" className="w-full">
                        <Download className="w-4 h-4 mr-2" />
                        Download Report
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </div>

            {/* Analysis History */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Analysis History</CardTitle>
                    <CardDescription>
                      Your recent crop analysis results
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analysisHistory.map((analysis) => {
                        const StatusIcon = getStatusIcon(analysis.status);
                        return (
                          <div key={analysis.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold">{analysis.cropName}</h4>
                              <StatusIcon className={`w-5 h-5 ${analysis.status === 'healthy' ? 'text-green-600' : analysis.status === 'warning' ? 'text-yellow-600' : 'text-red-600'}`} />
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{analysis.date}</p>
                            <div className="flex justify-between items-center text-sm">
                              <span className="text-gray-600">Confidence</span>
                              <span className="font-semibold">{analysis.confidence}%</span>
                            </div>
                            {analysis.issues.length > 0 && (
                              <div className="mt-2">
                                <p className="text-xs text-red-600">
                                  {analysis.issues.length} issue(s) detected
                                </p>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Tips Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mt-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Info className="w-5 h-5 mr-2 text-blue-600" />
                      Photography Tips
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="font-medium text-blue-800">Good Lighting</p>
                      <p className="text-blue-600">Take photos in natural daylight for best results</p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="font-medium text-green-800">Clear Focus</p>
                      <p className="text-green-600">Ensure the crop is in sharp focus</p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <p className="font-medium text-purple-800">Close-up Shots</p>
                      <p className="text-purple-600">Include leaves, stems, and any visible issues</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIAnalysis;
