import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ShoppingBasket, Building, MapPin, Calendar, Filter, Search, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { toast } from '@/components/ui/use-toast';

const mockDemands = [
  {
    id: 'D001',
    buyerName: 'Kivu Grand Hotel',
    buyerType: 'Hotel',
    productNeeded: 'Fresh Tomatoes',
    quantity: '500 kg',
    qualitySpec: 'Grade A, Organic',
    deliveryLocation: 'Goma, North Kivu',
    deliveryDate: '2025-06-15',
    contactPerson: 'Mr. <PERSON>',
    contactPhone: '+243 812 345 678',
    postedDate: '2025-06-01',
    status: 'Open'
  },
  {
    id: 'D002',
    buyerName: 'Le Gourmet Restaurant',
    buyerType: 'Restaurant',
    productNeeded: 'Sweet Potatoes',
    quantity: '200 kg',
    qualitySpec: 'Medium size, fresh',
    deliveryLocation: 'Bukavu, South Kivu',
    deliveryDate: '2025-06-20',
    contactPerson: 'Ms. Aisha',
    contactPhone: '+243 998 765 432',
    postedDate: '2025-06-03',
    status: 'Open'
  },
  {
    id: 'D003',
    buyerName: 'Kivu Catering Services',
    buyerType: 'Catering',
    productNeeded: 'Mixed Vegetables (Carrots, Cabbage, Onions)',
    quantity: '1000 kg total',
    qualitySpec: 'Fresh, assorted sizes',
    deliveryLocation: 'Goma, North Kivu',
    deliveryDate: '2025-06-10',
    contactPerson: 'Mr. David',
    contactPhone: '+243 888 111 222',
    postedDate: '2025-06-05',
    status: 'Partially Fulfilled'
  },
  {
    id: 'D004',
    buyerName: 'Hotel des Mille Collines (Kigali - Regional)',
    buyerType: 'Hotel',
    productNeeded: 'Avocados (Hass)',
    quantity: '300 kg weekly',
    qualitySpec: 'Export Grade A',
    deliveryLocation: 'Kigali, Rwanda (via Goma)',
    deliveryDate: 'Ongoing',
    contactPerson: 'Ms. Chantal',
    contactPhone: '+250 788 123 456',
    postedDate: '2025-05-28',
    status: 'Open'
  },
   {
    id: 'D005',
    buyerName: 'Bukavu Central Market Wholesalers',
    buyerType: 'Wholesaler',
    productNeeded: 'Maize (White)',
    quantity: '5 Tons',
    qualitySpec: 'Standard, dry',
    deliveryLocation: 'Bukavu, South Kivu',
    deliveryDate: '2025-06-25',
    contactPerson: 'Mr. Emmanuel',
    contactPhone: '+243 777 666 555',
    postedDate: '2025-06-06',
    status: 'Open'
  }
];

const DemandPortal = () => {
  const { t } = useLanguage();
  const [demands, setDemands] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProduct, setSelectedProduct] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');

  useEffect(() => {
    setDemands(mockDemands);
  }, []);

  const productTypes = ['all', ...new Set(mockDemands.map(d => d.productNeeded.split(' (')[0]))];
  const locations = ['all', ...new Set(mockDemands.map(d => d.deliveryLocation.split(', ')[0]))];

  const filteredDemands = demands.filter(demand => {
    const matchesSearch = demand.buyerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         demand.productNeeded.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesProduct = selectedProduct === 'all' || demand.productNeeded.toLowerCase().includes(selectedProduct.toLowerCase());
    const matchesLocation = selectedLocation === 'all' || demand.deliveryLocation.toLowerCase().includes(selectedLocation.toLowerCase());
    return matchesSearch && matchesProduct && matchesLocation;
  });

  const handleExpressInterest = (demandId) => {
    toast({
      title: t('demandportal.interest.title', 'Interest Expressed!'),
      description: t('demandportal.interest.description', `Your interest in demand ${demandId} has been noted. The buyer will be notified.`),
      variant: 'success',
    });
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'Open': return 'bg-green-500';
      case 'Partially Fulfilled': return 'bg-yellow-500';
      case 'Closed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };


  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2">
            {t('demandportal.title', 'Real-Time Buyer Demand')}
          </h1>
          <p className="text-gray-600 text-lg">
            {t('demandportal.subtitle', 'Connect with major restaurants, hotels, and wholesalers looking for agricultural products.')}
          </p>
        </motion.div>

        {/* Filters */}
        <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg mb-8">
          <CardHeader>
            <CardTitle className="text-green-700">
              {t('demandportal.filters.title', 'Filter Demands')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder={t('demandportal.filters.search', 'Search buyer or product...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                <SelectTrigger>
                  <SelectValue placeholder={t('demandportal.filters.product', 'Product Type')} />
                </SelectTrigger>
                <SelectContent>
                  {productTypes.map(product => (
                    <SelectItem key={product} value={product}>
                      {product === 'all' ? t('demandportal.filters.all_products', 'All Products') : product}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger>
                  <SelectValue placeholder={t('demandportal.filters.location', 'Delivery Location')} />
                </SelectTrigger>
                <SelectContent>
                  {locations.map(location => (
                    <SelectItem key={location} value={location}>
                      {location === 'all' ? t('demandportal.filters.all_locations', 'All Locations') : location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700">
                <Filter className="w-4 h-4 mr-2" />
                {t('demandportal.filters.apply', 'Apply Filters')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Demands List */}
        {filteredDemands.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDemands.map((demand) => (
              <motion.div
                key={demand.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.02 }}
                className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border-0 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 mb-1 flex items-center">
                        <ShoppingBasket className="w-5 h-5 mr-2 text-green-600" />
                        {demand.productNeeded}
                      </h3>
                      <p className="text-sm text-gray-500">Demand ID: {demand.id}</p>
                    </div>
                    <Badge className={`${getStatusColor(demand.status)} text-white`}>
                      {t(`demandportal.status.${demand.status.toLowerCase().replace(' ', '_')}`, demand.status)}
                    </Badge>
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center space-x-2 text-gray-700">
                      <Building className="w-4 h-4 text-green-600" />
                      <span>{demand.buyerName} ({demand.buyerType})</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-700">
                      <MapPin className="w-4 h-4 text-green-600" />
                      <span>{t('demandportal.delivery_to', 'Delivery to')}: {demand.deliveryLocation}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-700">
                      <Calendar className="w-4 h-4 text-green-600" />
                      <span>{t('demandportal.needed_by', 'Needed by')}: {demand.deliveryDate}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{t('demandportal.quantity', 'Quantity')}: {demand.quantity}</p>
                      <p className="text-sm text-gray-600">{t('demandportal.quality', 'Quality')}: {demand.qualitySpec}</p>
                    </div>
                     <p className="text-xs text-gray-500">{t('demandportal.posted_on', 'Posted on')}: {demand.postedDate}</p>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      {t('demandportal.view_details', 'View Details')}
                    </Button>
                    <Button 
                      size="sm" 
                      className="bg-gradient-to-r from-green-600 to-emerald-600"
                      onClick={() => handleExpressInterest(demand.id)}
                      disabled={demand.status !== 'Open'}
                    >
                      {t('demandportal.express_interest', 'Express Interest')}
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              {t('demandportal.no_demands_found.title', 'No Demands Found')}
            </h3>
            <p className="text-gray-500">
              {t('demandportal.no_demands_found.description', 'Try adjusting your search criteria or check back later for new opportunities.')}
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default DemandPortal;