import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { ShoppingCart, TrendingUp, Package, Clock, Search, Filter, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import Navigation from '@/components/Navigation';
import { useLanguage, useAuth } from '@kivu-smartfarm/shared';
import AddToCartButton from '@/components/AddToCartButton';

const BuyerDashboard = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [orders, setOrders] = useState([]);
  const [availableCrops, setAvailableCrops] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const storedOrders = localStorage.getItem('buyerOrders');
    if (storedOrders) {
      setOrders(JSON.parse(storedOrders));
    }

    const mockCrops = [
      {
        id: 1,
        name: 'Fresh Tomatoes',
        farmer: 'Jean Baptiste',
        location: 'Goma, Kivu',
        quantity: 500,
        price: 2.50,
        quality: 'Premium',
        rating: 4.8,
        harvestDate: '2024-01-15',
        certification: 'Organic'
      },
      {
        id: 2,
        name: 'Sweet Maize',
        farmer: 'Marie Uwimana',
        location: 'Bukavu, Kivu',
        quantity: 300,
        price: 1.80,
        quality: 'Standard',
        rating: 4.6,
        harvestDate: '2024-01-10',
        certification: 'GAP Certified'
      },
      {
        id: 3,
        name: 'Green Beans',
        farmer: 'Paul Mukamana',
        location: 'Uvira, Kivu',
        quantity: 200,
        price: 3.20,
        quality: 'Premium',
        rating: 4.9,
        harvestDate: '2024-01-12',
        certification: 'Organic'
      }
    ];
    setAvailableCrops(mockCrops);
  }, []);

  const placeOrder = (crop) => {
    const newOrder = {
      id: Date.now(),
      cropId: crop.id,
      cropName: crop.name,
      farmer: crop.farmer,
      quantity: 50, 
      totalPrice: crop.price * 50,
      status: 'pending',
      orderDate: new Date().toISOString().split('T')[0],
      expectedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    };

    const updatedOrders = [...orders, newOrder];
    setOrders(updatedOrders);
    localStorage.setItem('buyerOrders', JSON.stringify(updatedOrders));
  };

  const filteredCrops = availableCrops.filter(crop =>
    crop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    crop.farmer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const quickStats = [
    {
      title: 'Total Orders',
      value: orders.length,
      icon: ShoppingCart,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Pending Orders',
      value: orders.filter(order => order.status === 'pending').length,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      title: 'Monthly Spend',
      value: `${orders.reduce((sum, order) => sum + (order.totalPrice || 0), 0)}`,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Suppliers',
      value: new Set(orders.map(order => order.farmer)).size,
      icon: Package,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      <Navigation />
      
      <div className="pt-20 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <h1 className="text-3xl font-bold gradient-text mb-2">
              Welcome back, {user?.name || 'Buyer'}!
            </h1>
            <p className="text-gray-600">
              Discover fresh produce directly from local farmers in the Kivu region.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {quickStats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="card-hover">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 mb-1">{stat.title}</p>
                        <p className="text-2xl font-bold">{stat.value}</p>
                      </div>
                      <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                        <stat.icon className={`w-6 h-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle>Available Crops</CardTitle>
                      <CardDescription>
                        Fresh produce from verified farmers in your region
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          placeholder="Search crops or farmers..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10 w-64"
                        />
                      </div>
                      <Button variant="outline" size="sm">
                        <Filter className="w-4 h-4 mr-2" />
                        Filter
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {filteredCrops.map((crop) => (
                      <motion.div
                        key={crop.id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <img  
                              className="w-16 h-16 rounded-lg object-cover" 
                              alt={`${crop.name} from farmer`}
                             src="https://images.unsplash.com/photo-1658064618071-3cee70af9bf3" />
                            <div>
                              <h3 className="font-semibold text-lg">{crop.name}</h3>
                              <p className="text-sm text-gray-600">by {crop.farmer}</p>
                              <p className="text-xs text-gray-500">{crop.location}</p>
                              <div className="flex items-center mt-1">
                                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                <span className="text-sm text-gray-600 ml-1">{crop.rating}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-2xl font-bold text-green-600">${crop.price}</p>
                            <p className="text-sm text-gray-500">per kg</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                          <div>
                            <p className="text-gray-600">Available</p>
                            <p className="font-semibold">{crop.quantity} kg</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Quality</p>
                            <Badge variant={crop.quality === 'Premium' ? 'default' : 'secondary'}>
                              {crop.quality}
                            </Badge>
                          </div>
                          <div>
                            <p className="text-gray-600">Harvested</p>
                            <p className="font-semibold">{crop.harvestDate}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Certification</p>
                            <p className="font-semibold text-green-600">{crop.certification}</p>
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <Link to={`/product/${crop.id}`}>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </Link>
                          <div className="flex gap-2">
                            <Button
                              onClick={() => placeOrder(crop)}
                              variant="outline"
                              size="sm"
                            >
                              Order Now
                            </Button>
                            <AddToCartButton
                              product={crop}
                              className="gradient-bg"
                              size="sm"
                              showText={false}
                            />
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card className="card-hover">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ShoppingCart className="w-5 h-5 mr-2 text-blue-600" />
                    Recent Orders
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {orders.length === 0 ? (
                    <div className="text-center py-4">
                      <ShoppingCart className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 text-sm">No orders yet</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {orders.slice(-3).map((order) => (
                        <div key={order.id} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex justify-between items-start mb-2">
                            <p className="font-medium text-sm">{order.cropName}</p>
                            <Badge variant={order.status === 'pending' ? 'secondary' : 'default'}>
                              {order.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600">from {order.farmer}</p>
                          <p className="text-xs text-gray-600">{order.quantity} kg - ${order.totalPrice}</p>
                        </div>
                      ))}
                      <Button variant="outline" size="sm" className="w-full">
                        View All Orders
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="card-hover">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Star className="w-5 h-5 mr-2 text-yellow-600" />
                    Quality Assurance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm font-medium text-green-800">Certified Organic</p>
                    <p className="text-xs text-green-600">All organic produce verified</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm font-medium text-blue-800">Fresh Guarantee</p>
                    <p className="text-xs text-blue-600">Harvested within 48 hours</p>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <p className="text-sm font-medium text-purple-800">Traceability</p>
                    <p className="text-xs text-purple-600">Full supply chain tracking</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="card-hover">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
                    Market Trends
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Tomatoes</span>
                    <span className="text-sm font-semibold text-green-600">↑ 15%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Maize</span>
                    <span className="text-sm font-semibold text-red-600">↓ 8%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Beans</span>
                    <span className="text-sm font-semibold text-green-600">↑ 22%</span>
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-3">
                    View Market Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyerDashboard;