import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CreditCard, 
  Smartphone, 
  DollarSign, 
  MapPin, 
  User, 
  Mail, 
  Phone,
  ArrowLeft,
  Lock,
  CheckCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import Navigation from '@/components/Navigation';
import { useCart, useLanguage, useAuth } from '@kivu-smartfarm/shared';

const CheckoutPage = () => {
  const { items, totalPrice, clearCart } = useCart();
  const { t } = useLanguage();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [step, setStep] = useState(1); // 1: Details, 2: Payment, 3: Confirmation
  const [loading, setLoading] = useState(false);
  const [orderDetails, setOrderDetails] = useState({
    firstName: user?.name?.split(' ')[0] || '',
    lastName: user?.name?.split(' ')[1] || '',
    email: user?.email || '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    paymentMethod: ''
  });

  const paymentMethods = [
    {
      id: 'mobile_money',
      name: 'Mobile Money',
      description: 'Pay with M-Pesa, Orange Money, or Airtel Money',
      icon: Smartphone,
      popular: true
    },
    {
      id: 'stripe',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, American Express',
      icon: CreditCard,
      popular: false
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Pay with your PayPal account',
      icon: DollarSign,
      popular: false
    }
  ];

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const handleInputChange = (field, value) => {
    setOrderDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNextStep = () => {
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handlePreviousStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handlePlaceOrder = async () => {
    setLoading(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Clear cart and show success
    clearCart();
    setStep(3);
    setLoading(false);
  };

  const renderOrderSummary = () => (
    <Card className="sticky top-4">
      <CardHeader>
        <CardTitle className="flex items-center">
          <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
          {t('checkout.order_summary', 'Order Summary')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {items.map((item) => (
          <div key={item.id} className="flex justify-between items-center">
            <div className="flex-1">
              <h4 className="font-medium text-sm">{item.name}</h4>
              <p className="text-xs text-gray-500">
                {formatPrice(item.price)} × {item.quantity}
              </p>
            </div>
            <span className="font-medium">
              {formatPrice(item.price * item.quantity)}
            </span>
          </div>
        ))}
        
        <Separator />
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>{t('checkout.subtotal', 'Subtotal')}</span>
            <span>{formatPrice(totalPrice)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>{t('checkout.shipping', 'Shipping')}</span>
            <span>{formatPrice(5.00)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>{t('checkout.tax', 'Tax')}</span>
            <span>{formatPrice(totalPrice * 0.1)}</span>
          </div>
          <Separator />
          <div className="flex justify-between font-bold text-lg">
            <span>{t('checkout.total', 'Total')}</span>
            <span className="text-green-600">
              {formatPrice(totalPrice + 5.00 + (totalPrice * 0.1))}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (items.length === 0 && step !== 3) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-24 text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {t('checkout.empty_cart', 'Your cart is empty')}
          </h1>
          <p className="text-gray-600 mb-8">
            {t('checkout.empty_cart_description', 'Add some products to your cart before checking out')}
          </p>
          <Button onClick={() => navigate('/marketplace')} className="bg-green-600 hover:bg-green-700">
            {t('checkout.continue_shopping', 'Continue Shopping')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-24">
        {/* Header */}
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate(-1)}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('common.back', 'Back')}
          </Button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('checkout.title', 'Checkout')}
          </h1>
          
          {/* Progress Steps */}
          <div className="flex items-center space-x-4 mb-8">
            {[1, 2, 3].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNumber 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {stepNumber}
                </div>
                {stepNumber < 3 && (
                  <div className={`w-16 h-1 mx-2 ${
                    step > stepNumber ? 'bg-green-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {step === 1 && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <User className="w-5 h-5 mr-2" />
                      {t('checkout.shipping_details', 'Shipping Details')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">{t('checkout.first_name', 'First Name')}</Label>
                        <Input
                          id="firstName"
                          value={orderDetails.firstName}
                          onChange={(e) => handleInputChange('firstName', e.target.value)}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">{t('checkout.last_name', 'Last Name')}</Label>
                        <Input
                          id="lastName"
                          value={orderDetails.lastName}
                          onChange={(e) => handleInputChange('lastName', e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="email">{t('checkout.email', 'Email')}</Label>
                      <Input
                        id="email"
                        type="email"
                        value={orderDetails.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="phone">{t('checkout.phone', 'Phone Number')}</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={orderDetails.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="address">{t('checkout.address', 'Address')}</Label>
                      <Input
                        id="address"
                        value={orderDetails.address}
                        onChange={(e) => handleInputChange('address', e.target.value)}
                        required
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="city">{t('checkout.city', 'City')}</Label>
                        <Input
                          id="city"
                          value={orderDetails.city}
                          onChange={(e) => handleInputChange('city', e.target.value)}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="postalCode">{t('checkout.postal_code', 'Postal Code')}</Label>
                        <Input
                          id="postalCode"
                          value={orderDetails.postalCode}
                          onChange={(e) => handleInputChange('postalCode', e.target.value)}
                          required
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <div className="flex justify-end">
                  <Button onClick={handleNextStep} className="bg-green-600 hover:bg-green-700">
                    {t('checkout.continue_to_payment', 'Continue to Payment')}
                  </Button>
                </div>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Lock className="w-5 h-5 mr-2" />
                      {t('checkout.payment_method', 'Payment Method')}
                    </CardTitle>
                    <CardDescription>
                      {t('checkout.payment_description', 'Choose your preferred payment method')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {paymentMethods.map((method) => {
                      const IconComponent = method.icon;
                      return (
                        <div
                          key={method.id}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            orderDetails.paymentMethod === method.id
                              ? 'border-green-600 bg-green-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handleInputChange('paymentMethod', method.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <IconComponent className="w-6 h-6 text-gray-600" />
                              <div>
                                <div className="flex items-center space-x-2">
                                  <h3 className="font-medium">{method.name}</h3>
                                  {method.popular && (
                                    <Badge className="bg-green-600 text-white text-xs">
                                      {t('checkout.popular', 'Popular')}
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm text-gray-500">{method.description}</p>
                              </div>
                            </div>
                            <div className={`w-4 h-4 rounded-full border-2 ${
                              orderDetails.paymentMethod === method.id
                                ? 'border-green-600 bg-green-600'
                                : 'border-gray-300'
                            }`}>
                              {orderDetails.paymentMethod === method.id && (
                                <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>
                
                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePreviousStep}>
                    {t('common.back', 'Back')}
                  </Button>
                  <Button 
                    onClick={handlePlaceOrder}
                    disabled={!orderDetails.paymentMethod || loading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {loading ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {t('checkout.processing', 'Processing...')}
                      </div>
                    ) : (
                      t('checkout.place_order', 'Place Order')
                    )}
                  </Button>
                </div>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {t('checkout.order_confirmed', 'Order Confirmed!')}
                </h2>
                <p className="text-gray-600 mb-8">
                  {t('checkout.confirmation_message', 'Thank you for your order. You will receive a confirmation email shortly.')}
                </p>
                <div className="space-y-4">
                  <Button onClick={() => navigate('/buyer-dashboard')} className="bg-green-600 hover:bg-green-700">
                    {t('checkout.view_orders', 'View My Orders')}
                  </Button>
                  <Button variant="outline" onClick={() => navigate('/marketplace')}>
                    {t('checkout.continue_shopping', 'Continue Shopping')}
                  </Button>
                </div>
              </motion.div>
            )}
          </div>

          {/* Order Summary Sidebar */}
          {step !== 3 && (
            <div className="lg:col-span-1">
              {renderOrderSummary()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
