import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Star, ShoppingCart, ArrowLeft, MapPin, User, Calendar, ShieldCheck, Truck, MessageSquare, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@kivu-smartfarm/shared';
import ChatbotWidget from '@/components/ChatbotWidget';

const mockProducts = [
  {
    id: '1',
    name: 'Premium Tomatoes',
    farmer: '<PERSON>',
    location: 'Goma, North Kivu',
    category: 'vegetables',
    quantity: 500,
    price: 2.50,
    quality: 'Premium',
    rating: 4.8,
    reviews: 24,
    harvestDate: '2025-05-15',
    certification: 'Organic Certified',
    description: 'Fresh, juicy tomatoes grown using sustainable farming practices. Perfect for salads, sauces, and cooking. These tomatoes are hand-picked at peak ripeness to ensure maximum flavor and nutritional value.',
    images: [
      'Lush green tomato plants with ripe red tomatoes',
      'Close-up of a hand holding a fresh tomato',
      'Basket full of freshly harvested tomatoes'
    ],
    storageTips: 'Store in a cool, dry place. Do not refrigerate for best flavor. Consume within 5-7 days of purchase.',
    farmDetails: {
      name: 'Kivu Sunshine Farms',
      established: 2010,
      practices: ['Organic Farming', 'Water Conservation', 'Integrated Pest Management'],
      story: 'Kivu Sunshine Farms is dedicated to producing high-quality, organic produce while empowering local communities and promoting sustainable agriculture in the Kivu region.'
    }
  },
  {
    id: '2',
    name: 'Sweet Yellow Maize',
    farmer: 'Marie Uwimana',
    location: 'Bukavu, South Kivu',
    category: 'grains',
    quantity: 800,
    price: 1.80,
    quality: 'Standard',
    rating: 4.6,
    reviews: 18,
    harvestDate: '2025-05-10',
    certification: 'GAP Certified',
    description: 'High-quality yellow maize perfect for various culinary uses, including ugali, grilling, and roasting. Grown with care by experienced local farmers.',
    images: [
      'Field of golden yellow maize ready for harvest',
      'Close-up of a fresh cob of maize',
      'Sack of harvested maize kernels'
    ],
    storageTips: 'Store in a cool, dry place away from direct sunlight. Can be stored for longer periods if dried properly.',
    farmDetails: {
      name: 'Uwimana Family Farm',
      established: 1998,
      practices: ['Crop Rotation', 'Good Agricultural Practices (GAP)'],
      story: 'The Uwimana Family Farm has been cultivating maize for generations, focusing on quality and community support.'
    }
  },
];

const ProductDetailsPage = () => {
  const { t } = useLanguage();
  const { productId } = useParams();
  const product = mockProducts.find(p => p.id === productId);

  if (!product) {
    return (
      <div>
        <Navigation />
        <div className="container mx-auto px-4 py-8 pt-24 text-center">
          <h1 className="text-2xl font-bold text-red-600">Product not found</h1>
          <Link to="/marketplace">
            <Button variant="outline" className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Marketplace
            </Button>
          </Link>
        </div>
        <ChatbotWidget />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-100">
      <Navigation />
      <div className="container mx-auto px-4 py-8 pt-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Link to="/marketplace" className="inline-flex items-center text-green-600 hover:text-green-800 mb-6">
            <ArrowLeft className="mr-2 h-5 w-5" />
            {t('productDetails.backToMarketplace', 'Back to Marketplace')}
          </Link>

          <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
            {/* Product Images */}
            <div className="space-y-4">
              <motion.div whileHover={{ scale: 1.03 }} className="aspect-w-16 aspect-h-9">
                <img 
                  className="w-full h-auto object-cover rounded-xl shadow-lg"
                  alt={product.images[0]}
                 src="https://images.unsplash.com/photo-1671376354106-d8d21e55dddd" />
              </motion.div>
              <div className="grid grid-cols-2 gap-4">
                <motion.div whileHover={{ scale: 1.05 }}>
                  <img 
                    className="w-full h-auto object-cover rounded-lg shadow-md"
                    alt={product.images[1]}
                   src="https://images.unsplash.com/photo-1635865165118-917ed9e20936" />
                </motion.div>
                <motion.div whileHover={{ scale: 1.05 }}>
                  <img 
                    className="w-full h-auto object-cover rounded-lg shadow-md"
                    alt={product.images[2]}
                   src="https://images.unsplash.com/photo-1674482733144-d403b7628bb7" />
                </motion.div>
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <h1 className="text-4xl font-bold gradient-text">{product.name}</h1>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-6 h-6 ${i < Math.round(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                    />
                  ))}
                  <span className="ml-2 text-gray-600">({product.reviews} {t('productDetails.reviews', 'reviews')})</span>
                </div>
                <Badge variant={product.quality === 'Premium' ? 'default' : 'secondary'} className="text-sm px-3 py-1">
                  {product.quality}
                </Badge>
              </div>

              <p className="text-gray-700 text-lg leading-relaxed">{product.description}</p>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center text-gray-600">
                  <User className="w-5 h-5 mr-2 text-green-600" />
                  <span>{t('productDetails.farmer', 'Farmer')}: {product.farmer}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <MapPin className="w-5 h-5 mr-2 text-green-600" />
                  <span>{t('productDetails.location', 'Location')}: {product.location}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Calendar className="w-5 h-5 mr-2 text-green-600" />
                  <span>{t('productDetails.harvested', 'Harvested')}: {product.harvestDate}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <ShieldCheck className="w-5 h-5 mr-2 text-green-600" />
                  <span>{t('productDetails.certification', 'Certification')}: {product.certification}</span>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-6 space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-3xl font-bold text-green-700">${product.price.toFixed(2)} <span className="text-base font-normal text-gray-500">/{t('productDetails.perKg', 'per kg')}</span></p>
                  <p className="text-gray-600">{t('productDetails.available', 'Available')}: {product.quantity} kg</p>
                </div>
                <Button size="lg" className="w-full gradient-bg text-lg">
                  <ShoppingCart className="mr-2 h-5 w-5" />
                  {t('productDetails.addToCart', 'Add to Cart')}
                </Button>
                <Button variant="outline" size="lg" className="w-full text-lg">
                  <MessageSquare className="mr-2 h-5 w-5" />
                  {t('productDetails.contactSeller', 'Contact Seller')}
                </Button>
              </div>
            </div>
          </div>

          {/* Additional Details Tabs */}
          <div className="mt-12 lg:mt-16">
            <Card>
              <CardHeader>
                <CardTitle>{t('productDetails.additionalInfo', 'Additional Information')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-8">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">{t('productDetails.storageTips', 'Storage Tips')}</h3>
                  <p className="text-gray-600">{product.storageTips}</p>
                </div>
                
                <div className="border-t border-gray-200 pt-8">
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">{t('productDetails.farmDetails.title', 'Farm Details')}</h3>
                  <Card className="bg-green-50/50">
                    <CardContent className="p-6 space-y-3">
                      <p className="text-lg font-medium text-green-700">{product.farmDetails.name}</p>
                      <p className="text-sm text-gray-600">{t('productDetails.farmDetails.established', 'Established')}: {product.farmDetails.established}</p>
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-1">{t('productDetails.farmDetails.practices', 'Key Practices')}:</p>
                        <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                          {product.farmDetails.practices.map((practice, i) => <li key={i}>{practice}</li>)}
                        </ul>
                      </div>
                      <p className="text-sm text-gray-600 italic">"{product.farmDetails.story}"</p>
                    </CardContent>
                  </Card>
                </div>

                <div className="border-t border-gray-200 pt-8">
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">{t('productDetails.shippingInfo.title', 'Shipping Information')}</h3>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-start text-gray-600">
                      <Truck className="w-5 h-5 mr-2 mt-0.5 text-green-600 flex-shrink-0" />
                      <span>{t('productDetails.shippingInfo.availability', 'Shipping available to Goma, Bukavu, and Uvira. Standard delivery within 2-3 business days.')}</span>
                    </div>
                    <div className="flex items-start text-gray-600">
                      <Package className="w-5 h-5 mr-2 mt-0.5 text-green-600 flex-shrink-0" />
                      <span>{t('productDetails.shippingInfo.packaging', 'Packaged carefully to ensure freshness upon arrival. Eco-friendly packaging options available.')}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

        </motion.div>
      </div>
      <ChatbotWidget />
    </div>
  );
};

export default ProductDetailsPage;