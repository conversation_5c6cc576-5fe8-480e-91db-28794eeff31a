import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { Search, Filter, MapPin, Star, ShoppingCart, Eye, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@kivu-smartfarm/shared';
import AddToCartButton from '@/components/AddToCartButton';

const MarketPlace = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');

  const crops = [
    {
      id: 1,
      name: 'Premium Tomatoes',
      farmer: '<PERSON>mana',
      location: 'Goma, North Kivu',
      category: 'vegetables',
      quantity: 500,
      price: 2.50,
      quality: 'Premium',
      rating: 4.8,
      reviews: 24,
      harvestDate: '2024-01-15',
      certification: 'Organic Certified',
      description: 'Fresh, juicy tomatoes grown using sustainable farming practices.',
      image: 'Fresh red tomatoes in agricultural field'
    },
    {
      id: 2,
      name: 'Sweet Yellow Maize',
      farmer: 'Marie Uwimana',
      location: 'Bukavu, South Kivu',
      category: 'grains',
      quantity: 800,
      price: 1.80,
      quality: 'Standard',
      rating: 4.6,
      reviews: 18,
      harvestDate: '2024-01-10',
      certification: 'GAP Certified',
      description: 'High-quality yellow maize perfect for various culinary uses.',
      image: 'Golden yellow maize corn in farming field'
    },
    {
      id: 3,
      name: 'Organic Green Beans',
      farmer: 'Paul Mukamana',
      location: 'Uvira, South Kivu',
      category: 'vegetables',
      quantity: 200,
      price: 3.20,
      quality: 'Premium',
      rating: 4.9,
      reviews: 31,
      harvestDate: '2024-01-12',
      certification: 'Organic Certified',
      description: 'Tender, crisp green beans grown without pesticides.',
      image: 'Fresh green beans growing in organic farm'
    },
    {
      id: 4,
      name: 'Red Kidney Beans',
      farmer: 'Grace Nyirahabimana',
      location: 'Goma, North Kivu',
      category: 'legumes',
      quantity: 300,
      price: 2.80,
      quality: 'Premium',
      rating: 4.7,
      reviews: 22,
      harvestDate: '2024-01-08',
      certification: 'Organic Certified',
      description: 'Protein-rich red kidney beans, perfect for traditional dishes.',
      image: 'Red kidney beans in agricultural setting'
    },
    {
      id: 5,
      name: 'Fresh Cassava',
      farmer: 'Emmanuel Nzeyimana',
      location: 'Bukavu, South Kivu',
      category: 'tubers',
      quantity: 600,
      price: 1.50,
      quality: 'Standard',
      rating: 4.5,
      reviews: 15,
      harvestDate: '2024-01-14',
      certification: 'GAP Certified',
      description: 'Fresh cassava roots, a staple food rich in carbohydrates.',
      image: 'Fresh cassava roots harvested from farm'
    },
    {
      id: 6,
      name: 'Organic Spinach',
      farmer: 'Immaculée Mukashema',
      location: 'Uvira, South Kivu',
      category: 'vegetables',
      quantity: 150,
      price: 4.00,
      quality: 'Premium',
      rating: 4.8,
      reviews: 19,
      harvestDate: '2024-01-16',
      certification: 'Organic Certified',
      description: 'Nutrient-rich organic spinach leaves, freshly harvested.',
      image: 'Fresh organic spinach leaves in garden'
    }
  ];

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'vegetables', label: 'Vegetables' },
    { value: 'grains', label: 'Grains' },
    { value: 'legumes', label: 'Legumes' },
    { value: 'tubers', label: 'Tubers' },
    { value: 'fruits', label: 'Fruits' }
  ];

  const locations = [
    { value: 'all', label: 'All Locations' },
    { value: 'goma', label: 'Goma, North Kivu' },
    { value: 'bukavu', label: 'Bukavu, South Kivu' },
    { value: 'uvira', label: 'Uvira, South Kivu' }
  ];

  const filteredCrops = crops.filter(crop => {
    const matchesSearch = crop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         crop.farmer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || crop.category === selectedCategory;
    const matchesLocation = selectedLocation === 'all' || 
                           crop.location.toLowerCase().includes(selectedLocation);
    
    return matchesSearch && matchesCategory && matchesLocation;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      <Navigation />
      
      <div className="pt-20 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <h1 className="text-4xl font-bold gradient-text mb-4">
              {t('marketplace')}
            </h1>
            <p className="text-xl text-gray-600">
              Discover fresh, quality produce directly from local farmers in the Kivu region
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      placeholder="Search crops, farmers, or locations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-12 h-12 text-lg"
                    />
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="w-full sm:w-48 h-12">
                        <Filter className="w-4 h-4 mr-2" />
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                      <SelectTrigger className="w-full sm:w-48 h-12">
                        <MapPin className="w-4 h-4 mr-2" />
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map((location) => (
                          <SelectItem key={location.value} value={location.value}>
                            {location.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="mb-6"
          >
            <p className="text-gray-600">
              Showing {filteredCrops.length} of {crops.length} products
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCrops.map((crop, index) => (
              <motion.div
                key={crop.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <Card className="card-hover h-full">
                  <div className="relative">
                    <img 
                      className="w-full h-48 object-cover rounded-t-lg"
                      alt={`${crop.name} from ${crop.farmer}`}
                     src="https://images.unsplash.com/photo-1626347472833-cc1b65b9b470" />
                    <div className="absolute top-4 left-4">
                      <Badge variant={crop.quality === 'Premium' ? 'default' : 'secondary'}>
                        {crop.quality}
                      </Badge>
                    </div>
                    <div className="absolute top-4 right-4">
                      <Badge variant="outline" className="bg-white/90">
                        {crop.certification}
                      </Badge>
                    </div>
                  </div>

                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start mb-2">
                      <CardTitle className="text-xl">{crop.name}</CardTitle>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-green-600">${crop.price}</p>
                        <p className="text-sm text-gray-500">per kg</p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="w-4 h-4 mr-1" />
                        {crop.location}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="w-4 h-4 mr-1" />
                        Harvested: {crop.harvestDate}
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">
                          {crop.rating} ({crop.reviews} reviews)
                        </span>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <CardDescription className="mb-4">
                      {crop.description}
                    </CardDescription>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <p className="text-gray-600">Farmer</p>
                        <p className="font-semibold">{crop.farmer}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Available</p>
                        <p className="font-semibold">{crop.quantity} kg</p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Link to={`/product/${crop.id}`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          <Eye className="w-4 h-4 mr-2" />
                          Details
                        </Button>
                      </Link>
                      <AddToCartButton
                        product={crop}
                        className="flex-1 gradient-bg"
                        size="sm"
                      />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {filteredCrops.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No crops found</h3>
              <p className="text-gray-500">
                Try adjusting your search criteria or browse all available products
              </p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('all');
                  setSelectedLocation('all');
                }}
              >
                Clear Filters
              </Button>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketPlace;