import React, { useState } from 'react';
import ChatBot from 'react-chatbot-kit';
import 'react-chatbot-kit/build/main.css';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageSquare, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';

const ChatbotWidget = () => {
  const [showChatbot, setShowChatbot] = useState(false);
  const { t } = useLanguage();

  const config = {
    initialMessages: [
      {
        id: 1,
        message: t('chatbot.welcome', 'Hello! How can I help you today?'),
        widget: 'options',
      },
    ],
    botName: t('chatbot.botName', 'KivuSMART Bot'),
    customStyles: {
      botMessageBox: {
        backgroundColor: '#22c55e',
      },
      chatButton: {
        backgroundColor: '#16a34a',
      },
    },
    widgets: [
      {
        widgetName: 'options',
        widgetFunc: (props) => <Options {...props} />,
      },
    ],
  };

  const ActionProvider = ({ createChatBotMessage, setState, children }) => {
    const handleOption = (option) => {
      let botMessage;
      switch (option) {
        case 'marketplace':
          botMessage = createChatBotMessage(t('chatbot.marketplaceResponse', "You can find our marketplace by clicking 'Marketplace' in the navigation bar."));
          break;
        case 'support':
          botMessage = createChatBotMessage(t('chatbot.supportResponse', "For support, <NAME_EMAIL> or call +243 XXX XXX XXX."));
          break;
        case 'pricing':
          botMessage = createChatBotMessage(t('chatbot.pricingResponse', "Our subscription plans can be found on the 'Subscription' page."));
          break;
        default:
          botMessage = createChatBotMessage(t('chatbot.defaultResponse', "I'm sorry, I didn't understand that. Please choose an option or ask another question."));
      }
      setState((prev) => ({
        ...prev,
        messages: [...prev.messages, botMessage],
      }));
    };

    return (
      <div>
        {React.Children.map(children, (child) => {
          return React.cloneElement(child, {
            actions: {
              handleOption,
            },
          });
        })}
      </div>
    );
  };

  const MessageParser = ({ children, actions }) => {
    const parse = (message) => {
      const lowerCaseMessage = message.toLowerCase();
      if (lowerCaseMessage.includes(t('chatbot.keywords.marketplace', 'market')) || lowerCaseMessage.includes(t('chatbot.keywords.products', 'product'))) {
        actions.handleOption('marketplace');
      } else if (lowerCaseMessage.includes(t('chatbot.keywords.help', 'help')) || lowerCaseMessage.includes(t('chatbot.keywords.support', 'support'))) {
        actions.handleOption('support');
      } else if (lowerCaseMessage.includes(t('chatbot.keywords.price', 'price')) || lowerCaseMessage.includes(t('chatbot.keywords.subscription', 'subscription'))) {
        actions.handleOption('pricing');
      } else {
        actions.handleOption('default');
      }
    };

    return (
      <div>
        {React.Children.map(children, (child) => {
          return React.cloneElement(child, {
            parse: parse,
            actions,
          });
        })}
      </div>
    );
  };
  
  const Options = (props) => {
    const options = [
      { text: t('chatbot.option.marketplace', 'Browse Marketplace'), handler: () => props.actions.handleOption('marketplace'), id: 1 },
      { text: t('chatbot.option.support', 'Contact Support'), handler: () => props.actions.handleOption('support'), id: 2 },
      { text: t('chatbot.option.pricing', 'View Pricing'), handler: () => props.actions.handleOption('pricing'), id: 3 },
    ];
    return (
      <div className="flex flex-wrap gap-2 p-2">
        {options.map((option) => (
          <button key={option.id} onClick={option.handler} className="bg-green-500 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-green-600 transition-colors">
            {option.text}
          </button>
        ))}
      </div>
    );
  };


  return (
    <>
      <motion.div
        className="fixed bottom-6 right-6 z-[9998]"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.5, type: 'spring', stiffness: 260, damping: 20 }}
      >
        <Button
          size="icon"
          className="rounded-full w-14 h-14 gradient-bg shadow-lg"
          onClick={() => setShowChatbot((prev) => !prev)}
        >
          {showChatbot ? <X className="w-6 h-6" /> : <MessageSquare className="w-6 h-6" />}
        </Button>
      </motion.div>

      <AnimatePresence>
        {showChatbot && (
          <motion.div
            className="fixed bottom-24 right-6 z-[9997] w-[350px] h-[500px] shadow-2xl rounded-lg overflow-hidden"
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 50, scale: 0.8 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <ChatBot
              config={config}
              actionProvider={ActionProvider}
              messageParser={MessageParser}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ChatbotWidget;