import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCart } from '@kivu-smartfarm/shared';
import { useLanguage } from '@kivu-smartfarm/shared';

const AddToCartButton = ({ 
  product, 
  variant = "default", 
  size = "sm", 
  className = "",
  showText = true 
}) => {
  const { addItem } = useCart();
  const { t } = useLanguage();
  const [isAdded, setIsAdded] = useState(false);

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Add item to cart
    addItem({
      id: product.id,
      name: product.name || product.productName,
      price: parseFloat(product.price) || 0,
      image: product.image,
      farmer: product.farmer,
      location: product.location,
      category: product.category,
      quantity: 1
    });

    // Show success animation
    setIsAdded(true);
    setTimeout(() => setIsAdded(false), 2000);
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Button
        onClick={handleAddToCart}
        variant={variant}
        size={size}
        className={`${className} ${isAdded ? 'bg-green-600 text-white' : ''} transition-all duration-200`}
        disabled={isAdded}
      >
        <motion.div
          initial={false}
          animate={isAdded ? { rotate: 360 } : { rotate: 0 }}
          transition={{ duration: 0.3 }}
          className="mr-2"
        >
          {isAdded ? (
            <Check className="w-4 h-4" />
          ) : (
            <ShoppingCart className="w-4 h-4" />
          )}
        </motion.div>
        {showText && (
          <span>
            {isAdded 
              ? t('cart.added', 'Added!') 
              : t('cart.add_to_cart', 'Add to Cart')
            }
          </span>
        )}
      </Button>
    </motion.div>
  );
};

export default AddToCartButton;
