import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { UserPlus, Mail, Lock, User as UserIcon, Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import { useLanguage, useAuth } from '@kivu-smartfarm/shared';
import { toast } from '@/components/ui/use-toast';

const SignupPage = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();

  const queryParams = new URLSearchParams(location.search);
  const initialRole = queryParams.get('role') || 'farmer';

  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [role, setRole] = useState(initialRole);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (password !== confirmPassword) {
      toast({ title: t('passwordMismatchTitle', 'Password Mismatch'), description: t('passwordMismatchDesc', 'Passwords do not match.'), variant: 'destructive' });
      return;
    }
    setIsLoading(true);
    
    setTimeout(() => {
      const newUser = { email, name: fullName, type: role };
      login(newUser);
      toast({ title: t('signupSuccessTitle', 'Signup Successful!'), description: t('signupSuccessDesc', 'Welcome to Kivu SMARTFARM!') });
      navigate(role === 'farmer' ? '/farmer-dashboard' : '/buyer-dashboard');
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-100 flex flex-col">
      <Navigation />
      <div className="flex-grow flex items-center justify-center p-4 pt-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="w-full max-w-lg shadow-2xl glass-effect">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mb-4">
                <UserPlus className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-3xl font-bold gradient-text">{t('signup', 'Sign Up')}</CardTitle>
              <CardDescription>{t('signupPrompt', 'Create your Kivu SMARTFARM account')}</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <div className="relative">
                    <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="fullName"
                      type="text"
                      placeholder={t('fullNamePlaceholder', 'Enter your full name')}
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      required
                      className="pl-10 h-12 text-base"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="email"
                      type="email"
                      placeholder={t('emailPlaceholder', 'Enter your email')}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="pl-10 h-12 text-base"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="password"
                      type="password"
                      placeholder={t('passwordPlaceholder', 'Create a password')}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="pl-10 h-12 text-base"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder={t('confirmPasswordPlaceholder', 'Confirm your password')}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      className="pl-10 h-12 text-base"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Select value={role} onValueChange={setRole}>
                    <SelectTrigger className="h-12 text-base">
                      <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <div className="pl-6">
                        <SelectValue placeholder={t('rolePlaceholder', 'Select your role')} />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="farmer">{t('roleFarmer', 'Farmer')}</SelectItem>
                      <SelectItem value="buyer">{t('roleBuyer', 'Buyer')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button type="submit" className="w-full gradient-bg h-12 text-lg" disabled={isLoading}>
                  {isLoading ? t('loading', 'Loading...') : t('signup', 'Sign Up')}
                </Button>
              </form>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  {t('alreadyAccount', 'Already have an account?')}{' '}
                  <Link to="/login" className="font-medium text-green-600 hover:underline">
                    {t('login', 'Login')}
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default SignupPage;