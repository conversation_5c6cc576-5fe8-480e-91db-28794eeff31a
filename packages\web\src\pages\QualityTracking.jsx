
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Shield, Award, Eye, Truck, CheckCircle, AlertTriangle, Calendar, MapPin, User, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Navigation from '@/components/Navigation';
import { useLanguage } from '@kivu-smartfarm/shared';

const QualityTracking = () => {
  const { t } = useLanguage();
  const [selectedProduct, setSelectedProduct] = useState(null);

  const certifications = [
    {
      id: 'CERT001',
      productId: 'PRD001',
      productName: 'Premium Tomatoes',
      farmer: '<PERSON>',
      farmLocation: 'Goma, North Kivu',
      certificationDate: '2024-01-15',
      expiryDate: '2024-07-15',
      certifiedBy: 'Kivu Agricultural Standards Board',
      grade: 'A+',
      qualityScore: 95,
      tests: [
        { name: 'Pesticide Residue', result: 'Pass', value: '< 0.01 ppm' },
        { name: 'Heavy Metals', result: 'Pass', value: 'Within limits' },
        { name: 'Microbial Safety', result: 'Pass', value: 'Safe' },
        { name: 'Nutritional Content', result: 'Excellent', value: '98% standard' }
      ],
      supplyChain: [
        { stage: 'Farm Harvest', date: '2024-01-15', location: 'Goma Farm', status: 'completed' },
        { stage: 'Quality Inspection', date: '2024-01-15', location: 'Goma Facility', status: 'completed' },
        { stage: 'Packaging', date: '2024-01-16', location: 'Processing Center', status: 'completed' },
        { stage: 'Transportation', date: '2024-01-16', location: 'En Route', status: 'in_progress' },
        { stage: 'Delivery', date: '2024-01-17', location: 'Bukavu', status: 'pending' }
      ]
    },
    {
      id: 'CERT002',
      productId: 'PRD002',
      productName: 'Organic Green Beans',
      farmer: 'Marie Uwimana',
      farmLocation: 'Bukavu, South Kivu',
      certificationDate: '2024-01-14',
      expiryDate: '2024-07-14',
      certifiedBy: 'Organic Certification DRC',
      grade: 'A',
      qualityScore: 92,
      tests: [
        { name: 'Organic Compliance', result: 'Pass', value: '100% organic' },
        { name: 'Pesticide Residue', result: 'Pass', value: 'None detected' },
        { name: 'Soil Quality', result: 'Excellent', value: 'Rich nutrients' },
        { name: 'Freshness Index', result: 'Pass', value: '96% fresh' }
      ],
      supplyChain: [
        { stage: 'Farm Harvest', date: '2024-01-14', location: 'Bukavu Farm', status: 'completed' },
        { stage: 'Quality Inspection', date: '2024-01-14', location: 'Bukavu Facility', status: 'completed' },
        { stage: 'Packaging', date: '2024-01-15', location: 'Processing Center', status: 'completed' },
        { stage: 'Transportation', date: '2024-01-15', location: 'Completed', status: 'completed' },
        { stage: 'Delivery', date: '2024-01-16', location: 'Goma', status: 'completed' }
      ]
    },
    {
      id: 'CERT003',
      productId: 'PRD003',
      productName: 'Sweet Yellow Maize',
      farmer: 'Paul Mukamana',
      farmLocation: 'Uvira, South Kivu',
      certificationDate: '2024-01-13',
      expiryDate: '2024-07-13',
      certifiedBy: 'Kivu Agricultural Standards Board',
      grade: 'A',
      qualityScore: 89,
      tests: [
        { name: 'Aflatoxin Test', result: 'Pass', value: '< 4 ppb' },
        { name: 'Moisture Content', result: 'Pass', value: '14%' },
        { name: 'Protein Content', result: 'Good', value: '9.2%' },
        { name: 'Foreign Matter', result: 'Pass', value: '< 1%' }
      ],
      supplyChain: [
        { stage: 'Farm Harvest', date: '2024-01-13', location: 'Uvira Farm', status: 'completed' },
        { stage: 'Quality Inspection', date: '2024-01-13', location: 'Uvira Facility', status: 'completed' },
        { stage: 'Packaging', date: '2024-01-14', location: 'Processing Center', status: 'completed' },
        { stage: 'Transportation', date: '2024-01-14', location: 'Completed', status: 'completed' },
        { stage: 'Delivery', date: '2024-01-15', location: 'Goma', status: 'completed' }
      ]
    }
  ];

  const getGradeColor = (grade) => {
    switch (grade) {
      case 'A+': return 'bg-green-500';
      case 'A': return 'bg-blue-500';
      case 'B+': return 'bg-yellow-500';
      case 'B': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in_progress': return 'bg-blue-500';
      case 'pending': return 'bg-yellow-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getTestResultColor = (result) => {
    switch (result.toLowerCase()) {
      case 'pass': return 'text-green-600';
      case 'excellent': return 'text-green-700';
      case 'good': return 'text-blue-600';
      case 'fail': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            {t('quality.title', 'Quality Certification & Tracking')}
          </h1>
          <p className="text-gray-600 text-lg">
            {t('quality.subtitle', 'Track product quality, certifications, and supply chain transparency')}
          </p>
        </motion.div>

        {/* Search and Filters */}
        <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg mb-8">
          <CardHeader>
            <CardTitle className="text-blue-700">
              {t('quality.search.title', 'Search & Track Products')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input placeholder={t('quality.search.product_id', 'Product ID or Batch Number')} />
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder={t('quality.search.certification', 'Certification Type')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="organic">{t('quality.cert_types.organic', 'Organic')}</SelectItem>
                  <SelectItem value="standard">{t('quality.cert_types.standard', 'Standard')}</SelectItem>
                  <SelectItem value="premium">{t('quality.cert_types.premium', 'Premium')}</SelectItem>
                </SelectContent>
              </Select>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder={t('quality.search.grade', 'Quality Grade')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="a+">A+</SelectItem>
                  <SelectItem value="a">A</SelectItem>
                  <SelectItem value="b+">B+</SelectItem>
                  <SelectItem value="b">B</SelectItem>
                </SelectContent>
              </Select>
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                {t('quality.search.button', 'Search & Track')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Certifications Grid */}
        <div className="grid gap-6 mb-8">
          {certifications.map((cert) => (
            <motion.div
              key={cert.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.02 }}
              className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border-0 overflow-hidden"
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">
                      {cert.productName}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span className="flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        {cert.farmer}
                      </span>
                      <span className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {cert.farmLocation}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {cert.certificationDate}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Badge className={`${getGradeColor(cert.grade)} text-white`}>
                      {t('quality.grade', 'Grade')} {cert.grade}
                    </Badge>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-blue-600">{cert.qualityScore}%</p>
                      <p className="text-xs text-gray-500">{t('quality.score', 'Quality Score')}</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Quality Tests */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                      <Shield className="w-5 h-5 mr-2 text-blue-600" />
                      {t('quality.tests.title', 'Quality Tests')}
                    </h4>
                    <div className="space-y-2">
                      {cert.tests.map((test, index) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-800">{test.name}</p>
                            <p className="text-sm text-gray-600">{test.value}</p>
                          </div>
                          <Badge className={`${getTestResultColor(test.result)} bg-transparent border`}>
                            {test.result}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Supply Chain Tracking */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                      <Truck className="w-5 h-5 mr-2 text-purple-600" />
                      {t('quality.supply_chain.title', 'Supply Chain Tracking')}
                    </h4>
                    <div className="space-y-3">
                      {cert.supplyChain.map((stage, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded-full ${getStatusColor(stage.status)}`}>
                            {stage.status === 'completed' && (
                              <CheckCircle className="w-4 h-4 text-white" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-center">
                              <p className={`font-medium ${
                                stage.status === 'completed' ? 'text-green-700' : 
                                stage.status === 'in_progress' ? 'text-blue-700' : 'text-gray-500'
                              }`}>
                                {stage.stage}
                              </p>
                              <span className="text-xs text-gray-500">{stage.date}</span>
                            </div>
                            <p className="text-sm text-gray-600">{stage.location}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600">
                    <p><span className="font-medium">{t('quality.certified_by', 'Certified by')}:</span> {cert.certifiedBy}</p>
                    <p><span className="font-medium">{t('quality.expires', 'Expires')}:</span> {cert.expiryDate}</p>
                  </div>
                  <div className="flex space-x-3">
                    <Button variant="outline" size="sm">
                      {t('quality.download_cert', 'Download Certificate')}
                    </Button>
                    <Button 
                      size="sm" 
                      className="bg-gradient-to-r from-blue-600 to-purple-600"
                      onClick={() => setSelectedProduct(cert)}
                    >
                      {t('quality.view_details', 'View Full Details')}
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quality Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">{t('quality.stats.certified', 'Certified Products')}</p>
                <p className="text-3xl font-bold">1,247</p>
              </div>
              <Award className="w-12 h-12 text-green-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            delay={0.1}
            className="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">{t('quality.stats.avg_score', 'Avg Quality Score')}</p>
                <p className="text-3xl font-bold">92.5%</p>
              </div>
              <Shield className="w-12 h-12 text-blue-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            delay={0.2}
            className="bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">{t('quality.stats.tracked', 'Tracked Shipments')}</p>
                <p className="text-3xl font-bold">856</p>
              </div>
              <Eye className="w-12 h-12 text-purple-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            delay={0.3}
            className="bg-gradient-to-br from-orange-500 to-red-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">{t('quality.stats.compliance', 'Compliance Rate')}</p>
                <p className="text-3xl font-bold">98.7%</p>
              </div>
              <CheckCircle className="w-12 h-12 text-orange-200" />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default QualityTracking;
