
import React, { createContext, useContext, useState } from 'react';

const LanguageContext = createContext();

const translations = {
  en: {
    // Navigation
    home: 'Home',
    marketplace: 'Marketplace',
    aiAnalysis: 'AI Analysis',
    logistics: 'Logistics',
    farmerDashboard: 'Farmer Dashboard',
    buyerDashboard: 'Buyer Dashboard',
    subscription: 'Subscription',
    
    // Hero Section
    heroTitle: 'Transforming Agriculture in the Democratic Republic of Congo',
    heroSubtitle: 'Empowering local farmers through modern agricultural technologies, technical assistance, and direct market connections in the Kivu region.',
    getStarted: 'Get Started',
    learnMore: 'Learn More',
    
    // Features
    featuresTitle: 'Revolutionary Features',
    marketLinkage: 'Market Linkage Portal',
    marketLinkageDesc: 'Connect farmers directly with buyers - hotels, restaurants, and households for fair pricing.',
    aiVision: 'AI + Computer Vision',
    aiVisionDesc: 'Upload crop images for automated pest/disease detection and treatment recommendations.',
    supplyChain: 'Supply Chain Tracking',
    supplyChainDesc: 'End-to-end visibility for buyers on crop quality and source origin.',
    logistics: 'Logistics Coordination',
    logisticsDesc: 'Manage transportation and storage through integrated scheduling and tracking.',
    
    // Stats
    farmersConnected: 'Farmers Connected',
    buyersActive: 'Active Buyers',
    cropsAnalyzed: 'Crops Analyzed',
    successRate: 'Success Rate',
    
    // Common
    login: 'Login',
    signup: 'Sign Up',
    dashboard: 'Dashboard',
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Logout',
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    upload: 'Upload',
    download: 'Download',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',

    // Logistics
    logistics: {
      title: 'Logistics & Transportation',
      subtitle: 'Manage deliveries, track shipments, and coordinate transportation',
      tabs: {
        shipments: 'Shipments',
        drivers: 'Drivers',
        routes: 'Routes'
      },
      filters: {
        title: 'Filter Shipments',
        status: 'Status',
        search: 'Search by order...',
        apply: 'Apply Filters'
      },
      status: {
        all: 'All',
        pending: 'Pending',
        in_transit: 'In Transit',
        delivered: 'Delivered',
        cancelled: 'Cancelled'
      },
      pickup: 'Pickup',
      delivery: 'Delivery',
      farmer: 'Farmer',
      buyer: 'Buyer',
      driver: 'Driver',
      estimated_delivery: 'Est. Delivery',
      tracking: 'Tracking Progress',
      view_details: 'View Details',
      track_live: 'Track Live',
      drivers: {
        title: 'Driver Management',
        add_driver: 'Add Driver',
        name: 'Name',
        phone: 'Phone',
        vehicle: 'Vehicle',
        status: 'Status',
        available: 'Available',
        busy: 'Busy',
        offline: 'Offline',
        current_location: 'Current Location',
        total_deliveries: 'Total Deliveries',
        rating: 'Rating',
        assign_route: 'Assign Route',
        view_profile: 'View Profile'
      },
      routes: {
        title: 'Route Management',
        create_route: 'Create Route',
        route_name: 'Route Name',
        distance: 'Distance',
        duration: 'Duration',
        stops: 'Stops',
        optimize: 'Optimize Route',
        view_map: 'View Map'
      }
    },

    // Shopping Cart
    cart: {
      title: 'Shopping Cart',
      empty: 'Your cart is empty',
      empty_description: 'Add some products to start shopping',
      continue_shopping: 'Continue Shopping',
      add_to_cart: 'Add to Cart',
      added: 'Added!',
      remove: 'Remove',
      quantity: 'Quantity',
      subtotal: 'Subtotal',
      total: 'Total',
      proceed_to_checkout: 'Proceed to Checkout',
      items: 'items'
    },

    // Checkout
    checkout: {
      title: 'Checkout',
      order_summary: 'Order Summary',
      shipping_details: 'Shipping Details',
      payment_method: 'Payment Method',
      payment_description: 'Choose your preferred payment method',
      first_name: 'First Name',
      last_name: 'Last Name',
      email: 'Email',
      phone: 'Phone Number',
      address: 'Address',
      city: 'City',
      postal_code: 'Postal Code',
      subtotal: 'Subtotal',
      shipping: 'Shipping',
      tax: 'Tax',
      total: 'Total',
      continue_to_payment: 'Continue to Payment',
      place_order: 'Place Order',
      processing: 'Processing...',
      order_confirmed: 'Order Confirmed!',
      confirmation_message: 'Thank you for your order. You will receive a confirmation email shortly.',
      view_orders: 'View My Orders',
      continue_shopping: 'Continue Shopping',
      empty_cart: 'Your cart is empty',
      empty_cart_description: 'Add some products to your cart before checking out',
      popular: 'Popular'
    },

    // Payment
    payment: {
      mobile_money: 'Mobile Money Payment',
      mobile_money_description: 'Pay securely with your mobile money account',
      credit_card: 'Credit/Debit Card',
      card_description: 'Pay securely with your credit or debit card',
      paypal: 'PayPal',
      paypal_description: 'Pay with your PayPal account or credit card',
      select_provider: 'Select Provider',
      choose_provider: 'Choose your provider',
      phone_number: 'Phone Number',
      phone_help: 'Enter the phone number registered with your mobile money account',
      how_it_works: 'How it works',
      step1: 'Click "Pay Now" to initiate payment',
      step2: 'You will receive an SMS prompt on your phone',
      step3: 'Enter your mobile money PIN to confirm',
      step4: 'Payment will be processed instantly',
      amount_to_pay: 'Amount to Pay',
      pay_now: 'Pay Now',
      processing: 'Processing Payment...',
      check_phone: 'Please check your phone for the payment prompt',
      card_number: 'Card Number',
      expiry_date: 'Expiry Date',
      cvv: 'CVV',
      cardholder_name: 'Cardholder Name',
      secure_payment: 'Secure Payment',
      security_message: 'Your payment information is encrypted and secure. We never store your card details.',
      pay_securely: 'Pay Securely',
      paypal_benefits: 'PayPal Benefits',
      paypal_benefit1: 'Buyer protection on eligible purchases',
      paypal_benefit2: 'No need to share card details',
      paypal_benefit3: 'Pay with PayPal balance or linked cards',
      paypal_benefit4: 'Instant payment confirmation',
      pay_with_paypal: 'Pay with PayPal',
      opening_paypal: 'Opening PayPal...',
      paypal_redirect: 'You will be redirected to PayPal to complete your payment',
      paypal_popup: 'Please complete your payment in the PayPal popup window'
    },

    // Common
    common: {
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      close: 'Close',
      apply: 'Apply',
      confirm: 'Confirm'
    }
  },
  fr: {
    // Navigation
    home: 'Accueil',
    marketplace: 'Marché',
    aiAnalysis: 'Analyse IA',
    logistics: 'Logistique',
    farmerDashboard: 'Tableau de Bord Agriculteur',
    buyerDashboard: 'Tableau de Bord Acheteur',
    subscription: 'Abonnement',
    
    // Hero Section
    heroTitle: 'Transformer l\'Agriculture en République Démocratique du Congo',
    heroSubtitle: 'Autonomiser les agriculteurs locaux grâce aux technologies agricoles modernes, à l\'assistance technique et aux connexions directes au marché dans la région du Kivu.',
    getStarted: 'Commencer',
    learnMore: 'En Savoir Plus',
    
    // Features
    featuresTitle: 'Fonctionnalités Révolutionnaires',
    marketLinkage: 'Portail de Liaison Marché',
    marketLinkageDesc: 'Connecter directement les agriculteurs aux acheteurs - hôtels, restaurants et ménages pour des prix équitables.',
    aiVision: 'IA + Vision par Ordinateur',
    aiVisionDesc: 'Téléchargez des images de cultures pour la détection automatisée des ravageurs/maladies et des recommandations de traitement.',
    supplyChain: 'Suivi de la Chaîne d\'Approvisionnement',
    supplyChainDesc: 'Visibilité de bout en bout pour les acheteurs sur la qualité des cultures et l\'origine de la source.',
    logistics: 'Coordination Logistique',
    logisticsDesc: 'Gérer le transport et le stockage grâce à la planification et au suivi intégrés.',
    
    // Stats
    farmersConnected: 'Agriculteurs Connectés',
    buyersActive: 'Acheteurs Actifs',
    cropsAnalyzed: 'Cultures Analysées',
    successRate: 'Taux de Réussite',
    
    // Common
    login: 'Connexion',
    signup: 'S\'inscrire',
    dashboard: 'Tableau de Bord',
    profile: 'Profil',
    settings: 'Paramètres',
    logout: 'Déconnexion',
    save: 'Enregistrer',
    cancel: 'Annuler',
    edit: 'Modifier',
    delete: 'Supprimer',
    view: 'Voir',
    upload: 'Télécharger',
    download: 'Télécharger',
    search: 'Rechercher',
    filter: 'Filtrer',
    sort: 'Trier',
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    warning: 'Avertissement',
    info: 'Information',

    // Logistics
    logistics: {
      title: 'Logistique et Transport',
      subtitle: 'Gérer les livraisons, suivre les expéditions et coordonner le transport',
      tabs: {
        shipments: 'Expéditions',
        drivers: 'Chauffeurs',
        routes: 'Itinéraires'
      },
      filters: {
        title: 'Filtrer les Expéditions',
        status: 'Statut',
        search: 'Rechercher par commande...',
        apply: 'Appliquer les Filtres'
      },
      status: {
        all: 'Tous',
        pending: 'En Attente',
        in_transit: 'En Transit',
        delivered: 'Livré',
        cancelled: 'Annulé'
      },
      pickup: 'Ramassage',
      delivery: 'Livraison',
      farmer: 'Agriculteur',
      buyer: 'Acheteur',
      driver: 'Chauffeur',
      estimated_delivery: 'Livraison Est.',
      tracking: 'Suivi des Progrès',
      view_details: 'Voir Détails',
      track_live: 'Suivre en Direct',
      drivers: {
        title: 'Gestion des Chauffeurs',
        add_driver: 'Ajouter Chauffeur',
        name: 'Nom',
        phone: 'Téléphone',
        vehicle: 'Véhicule',
        status: 'Statut',
        available: 'Disponible',
        busy: 'Occupé',
        offline: 'Hors Ligne',
        current_location: 'Position Actuelle',
        total_deliveries: 'Total Livraisons',
        rating: 'Évaluation',
        assign_route: 'Assigner Itinéraire',
        view_profile: 'Voir Profil'
      },
      routes: {
        title: 'Gestion des Itinéraires',
        create_route: 'Créer Itinéraire',
        route_name: 'Nom de l\'Itinéraire',
        distance: 'Distance',
        duration: 'Durée',
        stops: 'Arrêts',
        optimize: 'Optimiser Itinéraire',
        view_map: 'Voir Carte'
      }
    },

    // Shopping Cart
    cart: {
      title: 'Panier d\'Achat',
      empty: 'Votre panier est vide',
      empty_description: 'Ajoutez des produits pour commencer vos achats',
      continue_shopping: 'Continuer les Achats',
      add_to_cart: 'Ajouter au Panier',
      added: 'Ajouté!',
      remove: 'Supprimer',
      quantity: 'Quantité',
      subtotal: 'Sous-total',
      total: 'Total',
      proceed_to_checkout: 'Procéder au Paiement',
      items: 'articles'
    },

    // Checkout
    checkout: {
      title: 'Commande',
      order_summary: 'Résumé de Commande',
      shipping_details: 'Détails de Livraison',
      payment_method: 'Méthode de Paiement',
      payment_description: 'Choisissez votre méthode de paiement préférée',
      first_name: 'Prénom',
      last_name: 'Nom',
      email: 'Email',
      phone: 'Numéro de Téléphone',
      address: 'Adresse',
      city: 'Ville',
      postal_code: 'Code Postal',
      subtotal: 'Sous-total',
      shipping: 'Livraison',
      tax: 'Taxe',
      total: 'Total',
      continue_to_payment: 'Continuer vers Paiement',
      place_order: 'Passer Commande',
      processing: 'Traitement...',
      order_confirmed: 'Commande Confirmée!',
      confirmation_message: 'Merci pour votre commande. Vous recevrez un email de confirmation sous peu.',
      view_orders: 'Voir Mes Commandes',
      continue_shopping: 'Continuer les Achats',
      empty_cart: 'Votre panier est vide',
      empty_cart_description: 'Ajoutez des produits à votre panier avant de commander',
      popular: 'Populaire'
    },

    // Payment
    payment: {
      mobile_money: 'Paiement Mobile Money',
      mobile_money_description: 'Payez en sécurité avec votre compte mobile money',
      credit_card: 'Carte de Crédit/Débit',
      card_description: 'Payez en sécurité avec votre carte de crédit ou débit',
      paypal: 'PayPal',
      paypal_description: 'Payez avec votre compte PayPal ou carte de crédit',
      select_provider: 'Sélectionner Fournisseur',
      choose_provider: 'Choisissez votre fournisseur',
      phone_number: 'Numéro de Téléphone',
      phone_help: 'Entrez le numéro enregistré avec votre compte mobile money',
      how_it_works: 'Comment ça marche',
      step1: 'Cliquez "Payer Maintenant" pour initier le paiement',
      step2: 'Vous recevrez un SMS sur votre téléphone',
      step3: 'Entrez votre PIN mobile money pour confirmer',
      step4: 'Le paiement sera traité instantanément',
      amount_to_pay: 'Montant à Payer',
      pay_now: 'Payer Maintenant',
      processing: 'Traitement du Paiement...',
      check_phone: 'Vérifiez votre téléphone pour l\'invite de paiement',
      card_number: 'Numéro de Carte',
      expiry_date: 'Date d\'Expiration',
      cvv: 'CVV',
      cardholder_name: 'Nom du Porteur',
      secure_payment: 'Paiement Sécurisé',
      security_message: 'Vos informations de paiement sont cryptées et sécurisées.',
      pay_securely: 'Payer en Sécurité',
      paypal_benefits: 'Avantages PayPal',
      paypal_benefit1: 'Protection acheteur sur achats éligibles',
      paypal_benefit2: 'Pas besoin de partager détails carte',
      paypal_benefit3: 'Payez avec solde PayPal ou cartes liées',
      paypal_benefit4: 'Confirmation de paiement instantanée',
      pay_with_paypal: 'Payer avec PayPal',
      opening_paypal: 'Ouverture PayPal...',
      paypal_redirect: 'Vous serez redirigé vers PayPal pour terminer',
      paypal_popup: 'Terminez votre paiement dans la fenêtre PayPal'
    },

    // Common
    common: {
      back: 'Retour',
      next: 'Suivant',
      previous: 'Précédent',
      close: 'Fermer',
      apply: 'Appliquer',
      confirm: 'Confirmer'
    }
  },
  sw: {
    // Navigation
    home: 'Nyumbani',
    marketplace: 'Soko',
    aiAnalysis: 'Uchambuzi wa AI',
    logistics: 'Usafirishaji',
    farmerDashboard: 'Dashibodi ya Mkulima',
    buyerDashboard: 'Dashibodi ya Mnunuzi',
    subscription: 'Usajili',
    
    // Hero Section
    heroTitle: 'Kubadilisha Kilimo katika Jamhuri ya Kidemokrasia ya Kongo',
    heroSubtitle: 'Kuwawezesha wakulima wa ndani kupitia teknolojia za kisasa za kilimo, msaada wa kiufundi, na miunganisho ya moja kwa moja ya soko katika mkoa wa Kivu.',
    getStarted: 'Anza',
    learnMore: 'Jifunze Zaidi',
    
    // Features
    featuresTitle: 'Vipengele vya Mapinduzi',
    marketLinkage: 'Mlango wa Kuunganisha Soko',
    marketLinkageDesc: 'Unganisha wakulima moja kwa moja na wanunuzi - hoteli, migahawa, na makazi kwa bei za haki.',
    aiVision: 'AI + Miwani ya Kompyuta',
    aiVisionDesc: 'Pakia picha za mazao kwa utambuzi wa otomatiki wa wadudu/magonjwa na mapendekezo ya matibabu.',
    supplyChain: 'Ufuatiliaji wa Mlolongo wa Ugavi',
    supplyChainDesc: 'Mwonekano wa mwisho hadi mwisho kwa wanunuzi juu ya ubora wa mazao na asili ya chanzo.',
    logistics: 'Uratibu wa Usafirishaji',
    logisticsDesc: 'Dhibiti usafirishaji na uhifadhi kupitia mipango na ufuatiliaji uliounganishwa.',
    
    // Stats
    farmersConnected: 'Wakulima Walioungana',
    buyersActive: 'Wanunuzi Hai',
    cropsAnalyzed: 'Mazao Yalichambuliwa',
    successRate: 'Kiwango cha Mafanikio',
    
    // Common
    login: 'Ingia',
    signup: 'Jisajili',
    dashboard: 'Dashibodi',
    profile: 'Wasifu',
    settings: 'Mipangilio',
    logout: 'Toka',
    save: 'Hifadhi',
    cancel: 'Ghairi',
    edit: 'Hariri',
    delete: 'Futa',
    view: 'Ona',
    upload: 'Pakia',
    download: 'Pakua',
    search: 'Tafuta',
    filter: 'Chuja',
    sort: 'Panga',
    loading: 'Inapakia...',
    error: 'Hitilafu',
    success: 'Mafanikio',
    warning: 'Onyo',
    info: 'Taarifa',

    // Logistics
    logistics: {
      title: 'Usafirishaji na Uchukuzi',
      subtitle: 'Dhibiti utoaji, fuatilia mizigo, na kuratibu usafirishaji',
      tabs: {
        shipments: 'Mizigo',
        drivers: 'Madereva',
        routes: 'Njia'
      },
      filters: {
        title: 'Chuja Mizigo',
        status: 'Hali',
        search: 'Tafuta kwa agizo...',
        apply: 'Tumia Vichujio'
      },
      status: {
        all: 'Yote',
        pending: 'Inasubiri',
        in_transit: 'Njiani',
        delivered: 'Imefikishwa',
        cancelled: 'Imeghairiwa'
      },
      pickup: 'Kuchukua',
      delivery: 'Utoaji',
      farmer: 'Mkulima',
      buyer: 'Mnunuzi',
      driver: 'Dereva',
      estimated_delivery: 'Utoaji wa Makadirio',
      tracking: 'Ufuatiliaji wa Maendeleo',
      view_details: 'Ona Maelezo',
      track_live: 'Fuatilia Moja kwa Moja',
      drivers: {
        title: 'Usimamizi wa Madereva',
        add_driver: 'Ongeza Dereva',
        name: 'Jina',
        phone: 'Simu',
        vehicle: 'Gari',
        status: 'Hali',
        available: 'Inapatikana',
        busy: 'Ameshughulika',
        offline: 'Nje ya Mtandao',
        current_location: 'Mahali pa Sasa',
        total_deliveries: 'Jumla ya Utoaji',
        rating: 'Ukadiriaji',
        assign_route: 'Kaba Njia',
        view_profile: 'Ona Wasifu'
      },
      routes: {
        title: 'Usimamizi wa Njia',
        create_route: 'Unda Njia',
        route_name: 'Jina la Njia',
        distance: 'Umbali',
        duration: 'Muda',
        stops: 'Vituo',
        optimize: 'Boresha Njia',
        view_map: 'Ona Ramani'
      }
    },

    // Shopping Cart
    cart: {
      title: 'Kikapu cha Ununuzi',
      empty: 'Kikapu chako ni tupu',
      empty_description: 'Ongeza bidhaa ili kuanza ununuzi',
      continue_shopping: 'Endelea Kununua',
      add_to_cart: 'Ongeza kwenye Kikapu',
      added: 'Imeongezwa!',
      remove: 'Ondoa',
      quantity: 'Kiasi',
      subtotal: 'Jumla Ndogo',
      total: 'Jumla',
      proceed_to_checkout: 'Endelea na Malipo',
      items: 'vitu'
    },

    // Checkout
    checkout: {
      title: 'Malipo',
      order_summary: 'Muhtasari wa Agizo',
      shipping_details: 'Maelezo ya Usafirishaji',
      payment_method: 'Njia ya Malipo',
      payment_description: 'Chagua njia yako ya malipo unayopendelea',
      first_name: 'Jina la Kwanza',
      last_name: 'Jina la Mwisho',
      email: 'Barua Pepe',
      phone: 'Nambari ya Simu',
      address: 'Anwani',
      city: 'Jiji',
      postal_code: 'Msimbo wa Posta',
      subtotal: 'Jumla Ndogo',
      shipping: 'Usafirishaji',
      tax: 'Kodi',
      total: 'Jumla',
      continue_to_payment: 'Endelea na Malipo',
      place_order: 'Weka Agizo',
      processing: 'Inachakata...',
      order_confirmed: 'Agizo Limethibitishwa!',
      confirmation_message: 'Asante kwa agizo lako. Utapokea barua pepe ya uthibitisho hivi karibuni.',
      view_orders: 'Ona Maagizo Yangu',
      continue_shopping: 'Endelea Kununua',
      empty_cart: 'Kikapu chako ni tupu',
      empty_cart_description: 'Ongeza bidhaa kwenye kikapu chako kabla ya kuagiza',
      popular: 'Maarufu'
    },

    // Payment
    payment: {
      mobile_money: 'Malipo ya Mobile Money',
      mobile_money_description: 'Lipa kwa usalama na akaunti yako ya mobile money',
      credit_card: 'Kadi ya Mkopo/Debit',
      card_description: 'Lipa kwa usalama na kadi yako ya mkopo au debit',
      paypal: 'PayPal',
      paypal_description: 'Lipa na akaunti yako ya PayPal au kadi ya mkopo',
      select_provider: 'Chagua Mtoa Huduma',
      choose_provider: 'Chagua mtoa huduma wako',
      phone_number: 'Nambari ya Simu',
      phone_help: 'Ingiza nambari iliyosajiliwa na akaunti yako ya mobile money',
      how_it_works: 'Jinsi inavyofanya kazi',
      step1: 'Bonyeza "Lipa Sasa" kuanza malipo',
      step2: 'Utapokea ujumbe wa SMS kwenye simu yako',
      step3: 'Ingiza PIN yako ya mobile money kuthibitisha',
      step4: 'Malipo yatachakatwa mara moja',
      amount_to_pay: 'Kiasi cha Kulipa',
      pay_now: 'Lipa Sasa',
      processing: 'Inachakata Malipo...',
      check_phone: 'Angalia simu yako kwa ujumbe wa malipo',
      card_number: 'Nambari ya Kadi',
      expiry_date: 'Tarehe ya Mwisho',
      cvv: 'CVV',
      cardholder_name: 'Jina la Mmiliki wa Kadi',
      secure_payment: 'Malipo Salama',
      security_message: 'Taarifa zako za malipo zimefichwa na ni salama.',
      pay_securely: 'Lipa kwa Usalama',
      paypal_benefits: 'Faida za PayPal',
      paypal_benefit1: 'Ulinzi wa mnunuzi kwa ununuzi unaostahili',
      paypal_benefit2: 'Hakuna haja ya kushiriki maelezo ya kadi',
      paypal_benefit3: 'Lipa na salio la PayPal au kadi zilizounganishwa',
      paypal_benefit4: 'Uthibitisho wa malipo wa papo hapo',
      pay_with_paypal: 'Lipa na PayPal',
      opening_paypal: 'Inafungua PayPal...',
      paypal_redirect: 'Utaelekezwa PayPal kukamilisha malipo',
      paypal_popup: 'Kamilisha malipo yako katika dirisha la PayPal'
    },

    // Common
    common: {
      back: 'Rudi',
      next: 'Ifuatayo',
      previous: 'Iliyotangulia',
      close: 'Funga',
      apply: 'Tumia',
      confirm: 'Thibitisha'
    }
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en');

  const t = (key, fallback = null) => {
    // Support nested keys like 'logistics.title'
    const keys = key.split('.');
    let value = translations[language];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // If key not found, return fallback or the original key
        return fallback || key;
      }
    }

    return value || fallback || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
