import React, { useState } from 'react';
import { ShoppingCart as ShoppingCartIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@kivu-smartfarm/shared';
import ShoppingCartComponent from './ShoppingCart';

const CartButton = () => {
  const { totalItems } = useCart();
  const [isCartOpen, setIsCartOpen] = useState(false);

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsCartOpen(true)}
        className="relative"
      >
        <ShoppingCartIcon className="w-5 h-5" />
        {totalItems > 0 && (
          <Badge 
            className="absolute -top-2 -right-2 bg-green-600 text-white text-xs min-w-[1.25rem] h-5 flex items-center justify-center rounded-full"
          >
            {totalItems > 99 ? '99+' : totalItems}
          </Badge>
        )}
      </Button>
      
      <ShoppingCartComponent 
        isOpen={isCartOpen} 
        onClose={() => setIsCartOpen(false)} 
      />
    </>
  );
};

export default CartButton;
