import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Smartphone, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@kivu-smartfarm/shared';

const MobileMoneyPayment = ({ amount, onSuccess, onError }) => {
  const { t } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [paymentData, setPaymentData] = useState({
    provider: '',
    phoneNumber: '',
    pin: ''
  });

  const providers = [
    { id: 'mpesa', name: '<PERSON>-<PERSON><PERSON><PERSON>', logo: '📱' },
    { id: 'orange', name: 'Orange Money', logo: '🟠' },
    { id: 'airtel', name: 'Airtel Money', logo: '🔴' }
  ];

  const handleInputChange = (field, value) => {
    setPaymentData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePayment = async () => {
    if (!paymentData.provider || !paymentData.phoneNumber) {
      onError('Please fill in all required fields');
      return;
    }

    setLoading(true);

    try {
      // Simulate mobile money payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Simulate success (90% success rate)
      if (Math.random() > 0.1) {
        onSuccess({
          transactionId: `MM${Date.now()}`,
          provider: paymentData.provider,
          phoneNumber: paymentData.phoneNumber,
          amount: amount
        });
      } else {
        throw new Error('Payment failed. Please try again.');
      }
    } catch (error) {
      onError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatPhoneNumber = (phone) => {
    // Remove any non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Format as +243 XXX XXX XXX (DRC format)
    if (cleaned.length >= 9) {
      return `+243 ${cleaned.slice(-9, -6)} ${cleaned.slice(-6, -3)} ${cleaned.slice(-3)}`;
    }
    return phone;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Smartphone className="w-5 h-5 mr-2 text-green-600" />
          {t('payment.mobile_money', 'Mobile Money Payment')}
        </CardTitle>
        <CardDescription>
          {t('payment.mobile_money_description', 'Pay securely with your mobile money account')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="provider">
            {t('payment.select_provider', 'Select Provider')}
          </Label>
          <Select 
            value={paymentData.provider} 
            onValueChange={(value) => handleInputChange('provider', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('payment.choose_provider', 'Choose your provider')} />
            </SelectTrigger>
            <SelectContent>
              {providers.map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  <div className="flex items-center space-x-2">
                    <span>{provider.logo}</span>
                    <span>{provider.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="phoneNumber">
            {t('payment.phone_number', 'Phone Number')}
          </Label>
          <Input
            id="phoneNumber"
            type="tel"
            placeholder="+243 XXX XXX XXX"
            value={paymentData.phoneNumber}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
            onBlur={(e) => {
              const formatted = formatPhoneNumber(e.target.value);
              handleInputChange('phoneNumber', formatted);
            }}
          />
          <p className="text-xs text-gray-500 mt-1">
            {t('payment.phone_help', 'Enter the phone number registered with your mobile money account')}
          </p>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">
                {t('payment.how_it_works', 'How it works')}
              </h4>
              <ol className="text-sm text-blue-800 mt-2 space-y-1">
                <li>1. {t('payment.step1', 'Click "Pay Now" to initiate payment')}</li>
                <li>2. {t('payment.step2', 'You will receive an SMS prompt on your phone')}</li>
                <li>3. {t('payment.step3', 'Enter your mobile money PIN to confirm')}</li>
                <li>4. {t('payment.step4', 'Payment will be processed instantly')}</li>
              </ol>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-4">
            <span className="font-medium">{t('payment.amount_to_pay', 'Amount to Pay')}</span>
            <span className="text-xl font-bold text-green-600">
              ${amount?.toFixed(2)}
            </span>
          </div>

          <Button
            onClick={handlePayment}
            disabled={loading || !paymentData.provider || !paymentData.phoneNumber}
            className="w-full bg-green-600 hover:bg-green-700"
            size="lg"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('payment.processing', 'Processing Payment...')}
              </div>
            ) : (
              <div className="flex items-center">
                <Smartphone className="w-4 h-4 mr-2" />
                {t('payment.pay_now', 'Pay Now')}
              </div>
            )}
          </Button>
        </div>

        {loading && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-yellow-50 p-4 rounded-lg"
          >
            <div className="flex items-center space-x-2">
              <div className="animate-pulse w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-yellow-800 text-sm">
                {t('payment.check_phone', 'Please check your phone for the payment prompt')}
              </span>
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
};

export default MobileMoneyPayment;
