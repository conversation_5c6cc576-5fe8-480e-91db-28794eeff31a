/* Global text centering and responsive utilities */

/* Text centering utilities */
.text-center-responsive {
  @apply text-center;
}

.content-center {
  @apply flex items-center justify-center;
}

.content-center-col {
  @apply flex flex-col items-center justify-center;
}

/* Responsive text sizing */
.heading-responsive {
  @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold;
}

.subheading-responsive {
  @apply text-lg sm:text-xl md:text-2xl font-semibold;
}

.body-responsive {
  @apply text-sm sm:text-base md:text-lg;
}

.caption-responsive {
  @apply text-xs sm:text-sm;
}

/* Card content centering */
.card-content-center {
  @apply flex flex-col items-center text-center p-6;
}

.card-header-center {
  @apply text-center;
}

/* Button responsive layouts */
.button-group-responsive {
  @apply flex flex-col sm:flex-row gap-2 sm:gap-4;
}

.button-full-mobile {
  @apply w-full sm:w-auto;
}

/* Section centering */
.section-center {
  @apply max-w-4xl mx-auto text-center px-4;
}

.section-header-center {
  @apply text-center mb-8;
}

/* Gradient text utility */
.gradient-text {
  @apply bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent;
}

/* Responsive spacing */
.spacing-responsive {
  @apply space-y-4 sm:space-y-6 md:space-y-8;
}

/* Container responsive */
.container-responsive {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

/* Navigation spacing fix */
.nav-item-center {
  @apply flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors whitespace-nowrap;
}

/* Mobile-first responsive grid */
.grid-responsive {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
}

.grid-responsive-2 {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6;
}

.grid-responsive-4 {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4;
}
