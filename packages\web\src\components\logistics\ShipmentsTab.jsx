import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Calendar, Phone, CheckCircle, Truck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@kivu-smartfarm/shared';

const shipmentsData = [
  {
    id: 'SH001',
    orderNumber: 'ORD-2024-001',
    farmer: '<PERSON>',
    buyer: 'Hotel Kivu Resort',
    product: 'Premium Tomatoes',
    quantity: '200 kg',
    pickup: 'Goma, North Kivu',
    delivery: 'Bukavu, South Kivu',
    status: 'in_transit',
    driver: '<PERSON>',
    driverPhone: '+243 123 456 789',
    estimatedDelivery: '2024-01-18',
    trackingSteps: [
      { step: 'Order Confirmed', completed: true, time: '2024-01-15 09:00' },
      { step: 'Picked Up', completed: true, time: '2024-01-16 14:30' },
      { step: 'In Transit', completed: true, time: '2024-01-17 08:00' },
      { step: 'Out for Delivery', completed: false, time: null },
      { step: 'Delivered', completed: false, time: null }
    ]
  },
  {
    id: 'SH002',
    orderNumber: 'ORD-2024-002',
    farmer: 'Marie Uwimana',
    buyer: 'Restaurant Le Panorama',
    product: 'Sweet Yellow Maize',
    quantity: '150 kg',
    pickup: 'Bukavu, South Kivu',
    delivery: 'Goma, North Kivu',
    status: 'pending',
    driver: null,
    driverPhone: null,
    estimatedDelivery: '2024-01-20',
    trackingSteps: [
      { step: 'Order Confirmed', completed: true, time: '2024-01-17 11:00' },
      { step: 'Picked Up', completed: false, time: null },
      { step: 'In Transit', completed: false, time: null },
      { step: 'Out for Delivery', completed: false, time: null },
      { step: 'Delivered', completed: false, time: null }
    ]
  },
  {
    id: 'SH003',
    orderNumber: 'ORD-2024-003',
    farmer: 'Paul Mukamana',
    buyer: 'Supermarket Fresh Foods',
    product: 'Organic Green Beans',
    quantity: '100 kg',
    pickup: 'Uvira, South Kivu',
    delivery: 'Goma, North Kivu',
    status: 'delivered',
    driver: 'Grace Nyirahabimana',
    driverPhone: '+243 987 654 321',
    estimatedDelivery: '2024-01-16',
    trackingSteps: [
      { step: 'Order Confirmed', completed: true, time: '2024-01-14 10:00' },
      { step: 'Picked Up', completed: true, time: '2024-01-15 13:00' },
      { step: 'In Transit', completed: true, time: '2024-01-15 16:00' },
      { step: 'Out for Delivery', completed: true, time: '2024-01-16 09:00' },
      { step: 'Delivered', completed: true, time: '2024-01-16 11:30' }
    ]
  }
];

const ShipmentsTab = () => {
  const { t } = useLanguage();

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500';
      case 'in_transit': return 'bg-blue-500';
      case 'delivered': return 'bg-green-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="text-green-700">
            {t('logistics.filters.title', 'Filter Shipments')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Select>
              <SelectTrigger>
                <SelectValue placeholder={t('logistics.filters.status', 'Status')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('logistics.status.all', 'All')}</SelectItem>
                <SelectItem value="pending">{t('logistics.status.pending', 'Pending')}</SelectItem>
                <SelectItem value="in_transit">{t('logistics.status.in_transit', 'In Transit')}</SelectItem>
                <SelectItem value="delivered">{t('logistics.status.delivered', 'Delivered')}</SelectItem>
              </SelectContent>
            </Select>
            <Input placeholder={t('logistics.filters.search', 'Search by order...')} />
            <Input type="date" />
            <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700">
              {t('logistics.filters.apply', 'Apply Filters')}
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6">
        {shipmentsData.map((shipment) => (
          <motion.div
            key={shipment.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ scale: 1.02 }}
            className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border-0 overflow-hidden"
          >
            <div className="p-6">
              <div className="flex flex-col sm:flex-row justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-1">
                    {shipment.orderNumber}
                  </h3>
                  <p className="text-sm text-gray-600">{shipment.product} - {shipment.quantity}</p>
                </div>
                <Badge className={`${getStatusColor(shipment.status)} text-white mt-2 sm:mt-0`}>
                  {t(`logistics.status.${shipment.status}`, shipment.status)}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-800 text-sm">
                        {t('logistics.pickup', 'Pickup')}: {shipment.pickup}
                      </p>
                      <p className="text-xs text-gray-600">
                        {t('logistics.farmer', 'Farmer')}: {shipment.farmer}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-blue-600 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-800 text-sm">
                        {t('logistics.delivery', 'Delivery')}: {shipment.delivery}
                      </p>
                      <p className="text-xs text-gray-600">
                        {t('logistics.buyer', 'Buyer')}: {shipment.buyer}
                      </p>
                    </div>
                  </div>
                  {shipment.driver && (
                    <div className="flex items-center space-x-3">
                      <Truck className="w-5 h-5 text-purple-600 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-gray-800 text-sm">
                          {t('logistics.driver', 'Driver')}: {shipment.driver}
                        </p>
                        <p className="text-xs text-gray-600 flex items-center">
                          <Phone className="w-3 h-3 mr-1" />
                          {shipment.driverPhone}
                        </p>
                      </div>
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-5 h-5 text-orange-600 flex-shrink-0" />
                    <p className="font-medium text-gray-800 text-sm">
                      {t('logistics.estimated_delivery', 'Est. Delivery')}: {shipment.estimatedDelivery}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-800 mb-3 text-sm">
                    {t('logistics.tracking', 'Tracking Progress')}
                  </h4>
                  <div className="space-y-3">
                    {shipment.trackingSteps.map((step, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded-full flex-shrink-0 ${
                          step.completed ? 'bg-green-500' : 'bg-gray-300'
                        }`}>
                          {step.completed && (
                            <CheckCircle className="w-4 h-4 text-white" />
                          )}
                        </div>
                        <div className="flex-1">
                          <p className={`font-medium text-xs ${
                            step.completed ? 'text-green-700' : 'text-gray-500'
                          }`}>
                            {step.step}
                          </p>
                          {step.time && (
                            <p className="text-xs text-gray-500">{step.time}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-6">
                <Button variant="outline" size="sm" className="w-full sm:w-auto">
                  {t('logistics.view_details', 'View Details')}
                </Button>
                <Button size="sm" className="bg-gradient-to-r from-green-600 to-emerald-600 w-full sm:w-auto">
                  {t('logistics.track_live', 'Track Live')}
                </Button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default ShipmentsTab;